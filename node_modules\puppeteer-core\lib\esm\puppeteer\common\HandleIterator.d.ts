/**
 * @license
 * Copyright 2023 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
import type { J<PERSON>Hand<PERSON> } from '../api/JSHandle.js';
import type { AwaitableIterable, HandleFor } from './types.js';
/**
 * @internal
 */
export declare function transposeIterableHandle<T>(handle: JSHandle<AwaitableIterable<T>>): AsyncIterableIterator<HandleFor<T>>;
//# sourceMappingURL=HandleIterator.d.ts.map