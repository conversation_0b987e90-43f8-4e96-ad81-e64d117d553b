/**
 * @license
 * Copyright 2020 Google Inc.
 * SPDX-License-Identifier: Apache-2.0
 */
import type { ElementHandle } from '../api/ElementHandle.js';
import { QueryHandler, type QuerySelector } from '../common/QueryHandler.js';
import type { AwaitableIterable } from '../common/types.js';
/**
 * @internal
 */
export declare class ARIA<PERSON>ueryHandler extends QueryHandler {
    static querySelector: QuerySelector;
    static queryAll(element: ElementHandle<Node>, selector: string): AwaitableIterable<ElementHandle<Node>>;
    static queryOne: (element: ElementHandle<Node>, selector: string) => Promise<ElementHandle<Node> | null>;
}
//# sourceMappingURL=AriaQueryHandler.d.ts.map