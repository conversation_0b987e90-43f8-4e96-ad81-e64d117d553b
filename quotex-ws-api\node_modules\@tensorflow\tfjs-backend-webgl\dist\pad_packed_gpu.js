/**
 * @license
 * Copyright 2019 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { getChannels } from './packing_util';
import { getCoordsDataType } from './shader_compiler';
export class PadPackedProgram {
    constructor(xShape, paddings, constantValue) {
        this.variableNames = ['x'];
        this.packedInputs = true;
        this.packedOutput = true;
        this.customUniforms = [{ name: 'value', type: 'float' }];
        this.outputShape = paddings.map((p, i) => p[0] /* beforePad */ + xShape[i] + p[1] /* afterPad */);
        const rank = xShape.length;
        const dtype = getCoordsDataType(rank);
        const start = paddings.map(p => p[0]).join(',');
        const end = paddings.map((p, i) => p[0] + xShape[i]).join(',');
        const coords = getChannels('rc', rank);
        const source = getChannels('source', rank);
        const cLimit = `${coords[rank - 1]} < ${this.outputShape[rank - 1]}`;
        const innerDims = rank === 1 ? 'source' : `vec2(${source.slice(-2).join()})`;
        const componentSetup = [
            `${dtype} rc = outputLoc;`, `${coords[rank - 1]} += 1;
       if(${cLimit}) {
      `,
            rank === 1 ? '' : `}
       rc = outputLoc;
       ${coords[rank - 2]} += 1;
       if(${coords[rank - 2]} < ${this.outputShape[rank - 2]}) {`,
            rank === 1 ? '' : `  ${coords[rank - 1]} += 1;
         if(${cLimit}) {`
        ];
        const paddingArea = rank === 1 ?
            'rc < start || rc >= end' :
            'any(lessThan(rc, start)) || any(greaterThanEqual(rc, end))';
        let mainLoop = '';
        for (let i = 0, j = rank === 1 ? 2 : 4; i < j; i++) {
            mainLoop += `
        ${componentSetup[i]}
        if (${paddingArea}) {
          result[${i}] = float(value);
        } else {
          ${dtype} source = rc - start;
          result[${i}] = getChannel(getX(${source.join()}), ${innerDims});
        }
      `;
        }
        mainLoop += (rank === 1 ? `} ` : `}}`);
        this.userCode = `
      const ${dtype} start = ${dtype}(${start});
      const ${dtype} end = ${dtype}(${end});

      void main() {
        ${dtype} outputLoc = getOutputCoords();
        vec4 result = vec4(0.);
        ${mainLoop}
        setOutput(result);
      }
    `;
    }
}
//# sourceMappingURL=data:application/json;base64,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