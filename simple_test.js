/**
 * اختبار بسيط للاتصال مع منصة Quotex
 * Simple Connection Test for Quotex Platform
 */

const { io } = require('socket.io-client');

async function testConnection() {
  console.log('🚀 بدء اختبار الاتصال البسيط...');
  
  try {
    // محاولة الاتصال
    const socket = io('https://ws.qxbroker.com', {
      transports: ['websocket'],
      timeout: 30000,
      forceNew: true
    });

    // الاستماع للأحداث
    socket.on('connect', () => {
      console.log('✅ تم الاتصال بنجاح!');
      console.log('Socket ID:', socket.id);
      
      // محاولة جلب قائمة الأزواج
      console.log('📋 محاولة جلب قائمة الأزواج...');
      socket.emit('instruments/list', {});
    });

    socket.on('disconnect', (reason) => {
      console.log('❌ تم قطع الاتصال:', reason);
    });

    socket.on('connect_error', (error) => {
      console.log('❌ خطأ في الاتصال:', error.message);
    });

    // الاستماع لجميع الأحداث
    socket.onAny((eventName, ...args) => {
      console.log(`📡 حدث: ${eventName}`, args);
    });

    // انتظار لمدة 30 ثانية
    setTimeout(() => {
      console.log('⏰ انتهت مدة الاختبار');
      socket.disconnect();
      process.exit(0);
    }, 30000);

  } catch (error) {
    console.error('❌ خطأ في الاختبار:', error);
    process.exit(1);
  }
}

// تشغيل الاختبار
testConnection();
