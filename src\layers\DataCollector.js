/**
 * طبقة جمع البيانات - Data Collector Layer
 * مسؤولة عن جمع البيانات التاريخية والبث المباشر للأسعار
 */

const EventEmitter = require('events');
const config = require('../config/config');
const Logger = require('../utils/Logger');

class DataCollector extends EventEmitter {
  constructor(connector) {
    super();
    
    this.connector = connector;
    this.logger = new Logger('DataCollector');
    
    // تخزين البيانات
    this.historicalData = new Map(); // pair -> candles[]
    this.liveData = new Map(); // pair -> latest price
    this.marketStatus = new Map(); // pair -> status info
    
    // إعدادات
    this.options = config.data;
    this.targetPairs = config.targetPairs;
    
    // حالة النظام
    this.isCollecting = false;
    this.subscribedPairs = new Set();
    
    this.setupEventHandlers();
  }

  /**
   * إعداد معالجات الأحداث
   */
  setupEventHandlers() {
    // الاستماع لتحديثات الأسعار
    this.connector.on('priceUpdate', (data) => {
      this.handlePriceUpdate(data);
    });

    // الاستماع لتحديثات الشموع
    this.connector.on('candleUpdate', (data) => {
      this.handleCandleUpdate(data);
    });

    // الاستماع لحالة الاتصال
    this.connector.on('connected', () => {
      this.onConnected();
    });

    this.connector.on('disconnected', () => {
      this.onDisconnected();
    });
  }

  /**
   * بدء جمع البيانات
   */
  async startCollection() {
    try {
      this.logger.info('بدء جمع البيانات...');
      
      if (!this.connector.isConnectionActive()) {
        throw new Error('الاتصال غير نشط');
      }

      // جلب قائمة الأزواج المتاحة
      await this.fetchAvailablePairs();
      
      // جلب البيانات التاريخية للأزواج المستهدفة
      await this.fetchHistoricalData();
      
      // الاشتراك في البث المباشر
      await this.subscribeToLiveData();
      
      this.isCollecting = true;
      this.emit('collectionStarted');
      
      this.logger.info('تم بدء جمع البيانات بنجاح');
      
    } catch (error) {
      this.logger.error('خطأ في بدء جمع البيانات:', error);
      throw error;
    }
  }

  /**
   * إيقاف جمع البيانات
   */
  async stopCollection() {
    this.logger.info('إيقاف جمع البيانات...');
    
    this.isCollecting = false;
    
    // إلغاء الاشتراك من البث المباشر
    for (const pair of this.subscribedPairs) {
      await this.unsubscribeFromPair(pair);
    }
    
    this.subscribedPairs.clear();
    this.emit('collectionStopped');
    
    this.logger.info('تم إيقاف جمع البيانات');
  }

  /**
   * جلب قائمة الأزواج المتاحة
   */
  async fetchAvailablePairs() {
    try {
      this.logger.info('جلب قائمة الأزواج المتاحة...');

      const response = await this.connector.send('instruments/list', {});

      if (response && Array.isArray(response)) {
        const availablePairs = response.map(instrument => ({
          symbol: instrument.symbol,
          name: instrument.name,
          type: instrument.type,
          status: instrument.status,
          payout: instrument.payout,
          change24: instrument.change24
        }));

        this.emit('pairsListUpdated', availablePairs);
        this.logger.info(`تم جلب ${availablePairs.length} زوج متاح`);

        return availablePairs;
      }

      throw new Error('استجابة غير صحيحة من الخادم');

    } catch (error) {
      this.logger.error('خطأ في جلب قائمة الأزواج:', error);
      throw error;
    }
  }

  /**
   * جلب البيانات التاريخية
   */
  async fetchHistoricalData() {
    this.logger.info('جلب البيانات التاريخية...');
    
    const promises = this.targetPairs.map(pair => 
      this.fetchPairHistoricalData(pair)
    );
    
    try {
      await Promise.all(promises);
      this.logger.info('تم جلب البيانات التاريخية لجميع الأزواج');
    } catch (error) {
      this.logger.error('خطأ في جلب البيانات التاريخية:', error);
      throw error;
    }
  }

  /**
   * جلب البيانات التاريخية لزوج واحد
   */
  async fetchPairHistoricalData(pair) {
    try {
      this.logger.debug(`جلب البيانات التاريخية للزوج: ${pair}`);

      const response = await this.connector.send('chart_notification/get', {
        asset: pair,
        interval: 'M1', // دقيقة واحدة
        offset: 0
      });

      if (response && Array.isArray(response)) {
        // تنظيم البيانات
        const candles = response.map(candle => ({
          timestamp: candle.time,
          open: parseFloat(candle.open),
          high: parseFloat(candle.high),
          low: parseFloat(candle.low),
          close: parseFloat(candle.close),
          volume: parseFloat(candle.volume || 0)
        })).sort((a, b) => a.timestamp - b.timestamp);

        // الاحتفاظ بآخر N شمعة فقط
        const limitedCandles = candles.slice(-this.options.historicalCandlesCount);

        this.historicalData.set(pair, limitedCandles);

        this.emit('historicalDataUpdated', {
          pair,
          candles: limitedCandles,
          count: limitedCandles.length
        });

        this.logger.debug(`تم جلب ${limitedCandles.length} شمعة للزوج ${pair}`);

        return limitedCandles;
      }

      throw new Error(`لا توجد بيانات للزوج ${pair}`);

    } catch (error) {
      this.logger.error(`خطأ في جلب البيانات التاريخية للزوج ${pair}:`, error);
      throw error;
    }
  }

  /**
   * الاشتراك في البث المباشر
   */
  async subscribeToLiveData() {
    this.logger.info('الاشتراك في البث المباشر...');
    
    for (const pair of this.targetPairs) {
      await this.subscribeToPair(pair);
    }
  }

  /**
   * الاشتراك في زوج واحد
   */
  async subscribeToPair(pair) {
    try {
      await this.connector.emit('instruments/follow', { asset: pair });
      this.subscribedPairs.add(pair);
      this.logger.debug(`تم الاشتراك في البث المباشر للزوج: ${pair}`);
    } catch (error) {
      this.logger.error(`خطأ في الاشتراك في الزوج ${pair}:`, error);
    }
  }

  /**
   * إلغاء الاشتراك من زوج
   */
  async unsubscribeFromPair(pair) {
    try {
      await this.connector.emit('instruments/unfollow', { asset: pair });
      this.subscribedPairs.delete(pair);
      this.logger.debug(`تم إلغاء الاشتراك من الزوج: ${pair}`);
    } catch (error) {
      this.logger.error(`خطأ في إلغاء الاشتراك من الزوج ${pair}:`, error);
    }
  }

  /**
   * معالجة تحديث السعر
   */
  handlePriceUpdate(data) {
    if (!data || !data.asset) return;
    
    const pair = data.asset;
    const price = parseFloat(data.value);
    const timestamp = data.time || Date.now();
    
    // تحديث السعر الحالي
    this.liveData.set(pair, {
      price,
      timestamp,
      bid: parseFloat(data.bid || price),
      ask: parseFloat(data.ask || price)
    });
    
    // إرسال إشعار بالتحديث
    this.emit('priceUpdated', {
      pair,
      price,
      timestamp
    });
  }

  /**
   * معالجة تحديث الشمعة
   */
  handleCandleUpdate(data) {
    if (!data || !data.asset) return;
    
    const pair = data.asset;
    const candle = {
      timestamp: data.time,
      open: parseFloat(data.open),
      high: parseFloat(data.high),
      low: parseFloat(data.low),
      close: parseFloat(data.close),
      volume: parseFloat(data.volume || 0)
    };
    
    // تحديث البيانات التاريخية
    this.updateHistoricalData(pair, candle);
    
    // إرسال إشعار بالتحديث
    this.emit('candleUpdated', {
      pair,
      candle
    });
  }

  /**
   * تحديث البيانات التاريخية
   */
  updateHistoricalData(pair, newCandle) {
    let candles = this.historicalData.get(pair) || [];
    
    // التحقق من وجود الشمعة (تحديث أم إضافة جديدة)
    const lastCandle = candles[candles.length - 1];
    
    if (lastCandle && lastCandle.timestamp === newCandle.timestamp) {
      // تحديث الشمعة الحالية
      candles[candles.length - 1] = newCandle;
    } else {
      // إضافة شمعة جديدة
      candles.push(newCandle);
      
      // الاحتفاظ بالحد الأقصى من الشموع
      if (candles.length > this.options.historicalCandlesCount) {
        candles = candles.slice(-this.options.historicalCandlesCount);
      }
    }
    
    this.historicalData.set(pair, candles);
  }

  /**
   * عند الاتصال
   */
  async onConnected() {
    if (this.isCollecting) {
      // إعادة الاشتراك في البث المباشر
      await this.subscribeToLiveData();
    }
  }

  /**
   * عند قطع الاتصال
   */
  onDisconnected() {
    this.subscribedPairs.clear();
  }

  /**
   * الحصول على البيانات التاريخية لزوج
   */
  getHistoricalData(pair) {
    return this.historicalData.get(pair) || [];
  }

  /**
   * الحصول على السعر الحالي لزوج
   */
  getCurrentPrice(pair) {
    return this.liveData.get(pair);
  }

  /**
   * الحصول على آخر شمعة لزوج
   */
  getLastCandle(pair) {
    const candles = this.getHistoricalData(pair);
    return candles.length > 0 ? candles[candles.length - 1] : null;
  }

  /**
   * التحقق من جودة البيانات
   */
  validateDataQuality(pair) {
    const candles = this.getHistoricalData(pair);
    const currentPrice = this.getCurrentPrice(pair);
    
    return {
      hasHistoricalData: candles.length > 0,
      historicalDataCount: candles.length,
      hasCurrentPrice: !!currentPrice,
      lastUpdate: currentPrice ? currentPrice.timestamp : null,
      isDataFresh: currentPrice ? (Date.now() - currentPrice.timestamp) < 60000 : false
    };
  }

  /**
   * الحصول على إحصائيات البيانات
   */
  getDataStats() {
    return {
      totalPairs: this.historicalData.size,
      subscribedPairs: this.subscribedPairs.size,
      isCollecting: this.isCollecting,
      totalCandles: Array.from(this.historicalData.values())
        .reduce((sum, candles) => sum + candles.length, 0)
    };
  }
}

module.exports = DataCollector;
