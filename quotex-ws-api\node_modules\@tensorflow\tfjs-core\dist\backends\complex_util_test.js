/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { ALL_ENVS, describeWithFlags } from '../jasmine_util';
import { expectArraysClose } from '../test_util';
import * as complex_util from './complex_util';
describe('complex_util', () => {
    it('mergeRealAndImagArrays', () => {
        const real = new Float32Array([1, 2, 3]);
        const imag = new Float32Array([4, 5, 6]);
        const complex = complex_util.mergeRealAndImagArrays(real, imag);
        expect(complex).toEqual(new Float32Array([1, 4, 2, 5, 3, 6]));
    });
    it('splitRealAndImagArrays', () => {
        const complex = new Float32Array([1, 4, 2, 5, 3, 6]);
        const result = complex_util.splitRealAndImagArrays(complex);
        expect(result.real).toEqual(new Float32Array([1, 2, 3]));
        expect(result.imag).toEqual(new Float32Array([4, 5, 6]));
    });
    it('complexWithEvenIndex', () => {
        const complex = new Float32Array([1, 2, 3, 4, 5, 6]);
        const result = complex_util.complexWithEvenIndex(complex);
        expect(result.real).toEqual(new Float32Array([1, 5]));
        expect(result.imag).toEqual(new Float32Array([2, 6]));
    });
    it('complexWithOddIndex', () => {
        const complex = new Float32Array([1, 2, 3, 4, 5, 6]);
        const result = complex_util.complexWithOddIndex(complex);
        expect(result.real).toEqual(new Float32Array([3]));
        expect(result.imag).toEqual(new Float32Array([4]));
    });
});
describeWithFlags('complex_util exponents', ALL_ENVS, () => {
    it('exponents inverse=false', () => {
        const inverse = false;
        const result = complex_util.exponents(5, inverse);
        expectArraysClose(result.real, new Float32Array([1, 0.30901700258255005]));
        expectArraysClose(result.imag, new Float32Array([0, -0.9510565400123596]));
    });
    it('exponents inverse=true', () => {
        const inverse = true;
        const result = complex_util.exponents(5, inverse);
        expectArraysClose(result.real, new Float32Array([1, 0.30901700258255005]));
        expectArraysClose(result.imag, new Float32Array([0, 0.9510565400123596]));
    });
});
describeWithFlags('complex_util assignment', ALL_ENVS, () => {
    it('assign complex value in TypedArray', () => {
        const t = new Float32Array(4);
        complex_util.assignToTypedArray(t, 1, 2, 0);
        complex_util.assignToTypedArray(t, 3, 4, 1);
        expectArraysClose(t, new Float32Array([1, 2, 3, 4]));
    });
});
//# sourceMappingURL=data:application/json;base64,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