"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.quantileSeqDocs = void 0;
var quantileSeqDocs = exports.quantileSeqDocs = {
  name: 'quantileSeq',
  category: 'Statistics',
  syntax: ['quantileSeq(A, prob[, sorted])', 'quantileSeq(A, [prob1, prob2, ...][, sorted])', 'quantileSeq(A, N[, sorted])'],
  description: 'Compute the prob order quantile of a matrix or a list with values. The sequence is sorted and the middle value is returned. Supported types of sequence values are: Number, BigNumber, Unit Supported types of probablity are: Number, BigNumber. \n\nIn case of a (multi dimensional) array or matrix, the prob order quantile of all elements will be calculated.',
  examples: ['quantileSeq([3, -1, 5, 7], 0.5)', 'quantileSeq([3, -1, 5, 7], [1/3, 2/3])', 'quantileSeq([3, -1, 5, 7], 2)', 'quantileSeq([-1, 3, 5, 7], 0.5, true)'],
  seealso: ['mean', 'median', 'min', 'max', 'prod', 'std', 'sum', 'variance']
};