{"version": 3, "file": "BrowserConnector.js", "sourceRoot": "", "sources": ["../../../../src/cdp/BrowserConnector.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAOH,+CAA+D;AAE/D,6CAAwC;AACxC,mDAA2C;AAE3C;;;;;GAKG;AACI,KAAK,UAAU,oBAAoB,CACxC,mBAAwC,EACxC,GAAW,EACX,OAA+C;IAE/C,MAAM,EACJ,iBAAiB,GAAG,KAAK,EACzB,eAAe,GAAG,0BAAgB,EAClC,YAAY,EACZ,aAAa,EAAE,YAAY,EAC3B,MAAM,GAAG,CAAC,EACV,eAAe,GAChB,GAAG,OAAO,CAAC;IAEZ,MAAM,UAAU,GAAG,IAAI,0BAAU,CAC/B,GAAG,EACH,mBAAmB,EACnB,MAAM,EACN,eAAe,CAChB,CAAC;IAEF,MAAM,OAAO,GAAG,MAAM,UAAU,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;IAC5D,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC;QAC/D,CAAC,CAAC,SAAS;QACX,CAAC,CAAC,QAAQ,CAAC;IAEb,MAAM,EAAC,iBAAiB,EAAC,GAAG,MAAM,UAAU,CAAC,IAAI,CAC/C,2BAA2B,CAC5B,CAAC;IACF,MAAM,OAAO,GAAG,MAAM,uBAAU,CAAC,OAAO,CACtC,OAAO,IAAI,QAAQ,EACnB,UAAU,EACV,iBAAiB,EACjB,iBAAiB,EACjB,eAAe,EACf,SAAS,EACT,GAAG,EAAE;QACH,OAAO,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,oBAAU,CAAC,CAAC;IAC5D,CAAC,EACD,YAAY,EACZ,YAAY,CACb,CAAC;IACF,OAAO,OAAO,CAAC;AACjB,CAAC;AA3CD,oDA2CC"}