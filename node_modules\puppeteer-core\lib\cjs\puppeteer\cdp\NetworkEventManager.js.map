{"version": 3, "file": "NetworkEventManager.js", "sourceRoot": "", "sources": ["../../../../src/cdp/NetworkEventManager.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAkCH;;;;GAIG;AACH,MAAa,mBAAmB;IAC9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+BG;IACH,qBAAqB,GAAG,IAAI,GAAG,EAG5B,CAAC;IACJ,iBAAiB,GAAG,IAAI,GAAG,EAGxB,CAAC;IACJ,gBAAgB,GAAG,IAAI,GAAG,EAAoC,CAAC;IAE/D;;;;;;;;OAQG;IACH,6BAA6B,GAAG,IAAI,GAAG,EAGpC,CAAC;IACJ,sBAAsB,GAAG,IAAI,GAAG,EAAsC,CAAC;IACvE,oBAAoB,GAAG,IAAI,GAAG,EAAsC,CAAC;IAErE,MAAM,CAAC,gBAAkC;QACvC,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QACpD,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QAChD,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QACnD,IAAI,CAAC,sBAAsB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QACrD,IAAI,CAAC,6BAA6B,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IAC9D,CAAC;IAED,iBAAiB,CACf,gBAAkC;QAElC,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,gBAAgB,CAAC,EAAE,CAAC;YAC9D,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAAC,gBAAgB,EAAE,EAAE,CAAC,CAAC;QAC/D,CAAC;QACD,OAAO,IAAI,CAAC,6BAA6B,CAAC,GAAG,CAC3C,gBAAgB,CACoC,CAAC;IACzD,CAAC;IAEO,kBAAkB,CAAC,cAA8B;QACvD,IAAI,CAAC,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE,CAAC;YACrD,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC;QACtD,CAAC;QACD,OAAO,IAAI,CAAC,sBAAsB,CAAC,GAAG,CAAC,cAAc,CAAqB,CAAC;IAC7E,CAAC;IAED,iBAAiB,CACf,cAA8B,EAC9B,YAA0B;QAE1B,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC7D,CAAC;IAED,sBAAsB,CACpB,cAA8B;QAE9B,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC,KAAK,EAAE,CAAC;IACzD,CAAC;IAED,qBAAqB;QACnB,IAAI,sBAAsB,GAAG,CAAC,CAAC;QAC/B,KAAK,MAAM,OAAO,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,EAAE,CAAC;YACrD,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;gBACxB,sBAAsB,EAAE,CAAC;YAC3B,CAAC;QACH,CAAC;QACD,OAAO,sBAAsB,CAAC;IAChC,CAAC;IAED,sBAAsB,CACpB,gBAAkC,EAClC,KAA8C;QAE9C,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;IAC1D,CAAC;IAED,oBAAoB,CAClB,gBAAkC;QAElC,OAAO,IAAI,CAAC,qBAAqB,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IAC1D,CAAC;IAED,uBAAuB,CAAC,gBAAkC;QACxD,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IACtD,CAAC;IAED,gBAAgB,CACd,gBAAkC;QAElC,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IACtD,CAAC;IAED,mBAAmB,CAAC,gBAAkC;QACpD,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IAClD,CAAC;IAED,kBAAkB,CAChB,gBAAkC,EAClC,KAAwC;QAExC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;IACtD,CAAC;IAED,UAAU,CAAC,gBAAkC;QAC3C,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IACrD,CAAC;IAED,YAAY,CACV,gBAAkC,EAClC,OAAuB;QAEvB,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;IACvD,CAAC;IAED,aAAa,CAAC,gBAAkC;QAC9C,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IACjD,CAAC;IAED,mBAAmB,CACjB,gBAAkC;QAElC,OAAO,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;IACzD,CAAC;IAED,eAAe,CACb,gBAAkC,EAClC,KAAuB;QAEvB,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;IACzD,CAAC;IAED,sBAAsB,CAAC,gBAAkC;QACvD,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;IACrD,CAAC;CACF;AA7KD,kDA6KC"}