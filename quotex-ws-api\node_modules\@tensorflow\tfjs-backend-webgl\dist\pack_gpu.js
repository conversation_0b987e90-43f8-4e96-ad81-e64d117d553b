/**
 * @license
 * Copyright 2018 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { useShapeUniforms } from './gpgpu_math';
import { getChannels } from './packing_util';
import { getCoordsDataType } from './shader_compiler';
export class PackProgram {
    constructor(outputShape) {
        this.variableNames = ['A'];
        this.packedInputs = false;
        this.packedOutput = true;
        // Only input / output 3D tensors.
        this.outputShape = outputShape;
        this.rank = outputShape.length;
        this.enableShapeUniforms = useShapeUniforms(this.outputShape.length);
        if (this.rank === 0) {
            this.userCode = `
        void main() {
          setOutput(vec4(getA(), 0., 0., 0.));
        }
      `;
        }
        else {
            const channels = getChannels('rc', this.rank);
            const dtype = getCoordsDataType(this.rank);
            const outOfBoundsCondition = this.getOutOfBoundsCondition(channels);
            const setup = this.getSetup(channels);
            const output = this.getOutput(channels);
            this.userCode = `
        void main() {
          ${dtype} rc = getOutputCoords();

          if(${outOfBoundsCondition}) {
            setOutput(vec4(0));
          } else {
            ${setup}

            setOutput(vec4(${output}));
          }
        }
      `;
        }
    }
    getSourceCoordsArr(dims) {
        const coords = [];
        for (let row = 0; row <= 1; row++) {
            for (let col = 0; col <= 1; col++) {
                let coord = `${row === 0 ? 'r' : 'rp1'}, ${col === 0 ? 'c' : 'cp1'}`;
                for (let d = 2; d < this.rank; d++) {
                    coord = `${dims[dims.length - 1 - d]},` + coord;
                }
                coords.push(coord);
            }
        }
        return coords;
    }
    getOutOfBoundsCondition(dims) {
        if (this.rank === 1) {
            return `rc > ${this.enableShapeUniforms ? 'outShape' : this.outputShape[0]}`;
        }
        let cond = '';
        for (let i = this.rank - 2; i < this.rank; i++) {
            cond += `${dims[i]} >= ${this.enableShapeUniforms ? `outShape[${i}]` : this.outputShape[i]}`;
            if (i < this.rank - 1) {
                cond += '||';
            }
        }
        return cond;
    }
    getSetup(dims) {
        if (this.rank === 1) {
            return '';
        }
        const innerDims = dims.slice(-2);
        const col = this.enableShapeUniforms ? `outShape[${this.rank} - 1]` :
            this.outputShape[this.rank - 1];
        const row = this.enableShapeUniforms ? `outShape[${this.rank} - 2]` :
            this.outputShape[this.rank - 2];
        return `
      int r = ${innerDims[0]};
      int c = ${innerDims[1]};
      int rp1 = r + 1;
      int cp1 = c + 1;

      bool cEdge = cp1 >= ${col};
      bool rEdge = rp1 >= ${row};
    `;
    }
    getOutput(dims) {
        const sourceCoords = this.getSourceCoordsArr(dims);
        if (this.rank === 1) {
            const outShape = this.enableShapeUniforms ? 'outShape' : this.outputShape[0];
            return `getA(rc), (rc + 1 >= ${outShape} ? 0. : getA(rc + 1)), 0, 0`;
        }
        return `getA(${sourceCoords[0]}),
            cEdge ? 0. : getA(${sourceCoords[1]}),
            rEdge ? 0. : getA(${sourceCoords[2]}),
            rEdge || cEdge ? 0. : getA(${sourceCoords[3]})`;
    }
}
//# sourceMappingURL=data:application/json;base64,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