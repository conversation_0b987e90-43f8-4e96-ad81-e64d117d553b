/**
 * @license
 * Copyright 2017 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
import { env } from '@tensorflow/tfjs-core';
import { getGlslDifferences } from './glsl_version';
import * as tex_util from './tex_util';
import * as webgl_util from './webgl_util';
export function createVertexShader(gl) {
    const glsl = getGlslDifferences();
    const vertexShaderSource = `${glsl.version}
    precision highp float;
    ${glsl.attribute} vec3 clipSpacePos;
    ${glsl.attribute} vec2 uv;
    ${glsl.varyingVs} vec2 resultUV;

    void main() {
      gl_Position = vec4(clipSpacePos, 1);
      resultUV = uv;
    }`;
    return webgl_util.createVertexShader(gl, vertexShaderSource);
}
export function createVertexBuffer(gl) {
    // [x y z u v] * [upper-left, lower-left, upper-right, lower-right]
    const vertexArray = new Float32Array([-1, 1, 0, 0, 1, -1, -1, 0, 0, 0, 1, 1, 0, 1, 1, 1, -1, 0, 1, 0]);
    return webgl_util.createStaticVertexBuffer(gl, vertexArray);
}
export function createIndexBuffer(gl) {
    // OpenGL (and WebGL) have "CCW == front" winding
    const triangleVertexIndices = new Uint16Array([0, 1, 2, 2, 1, 3]);
    return webgl_util.createStaticIndexBuffer(gl, triangleVertexIndices);
}
function createAndConfigureTexture(gl, width, height, internalFormat, textureFormat, textureType) {
    webgl_util.validateTextureSize(width, height);
    const texture = webgl_util.createTexture(gl);
    const tex2d = gl.TEXTURE_2D;
    webgl_util.callAndCheck(gl, () => gl.bindTexture(tex2d, texture));
    webgl_util.callAndCheck(gl, () => gl.texParameteri(tex2d, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE));
    webgl_util.callAndCheck(gl, () => gl.texParameteri(tex2d, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE));
    webgl_util.callAndCheck(gl, () => gl.texParameteri(tex2d, gl.TEXTURE_MIN_FILTER, gl.NEAREST));
    webgl_util.callAndCheck(gl, () => gl.texParameteri(tex2d, gl.TEXTURE_MAG_FILTER, gl.NEAREST));
    if (env().getNumber('WEBGL_VERSION') === 1) {
        webgl_util.callAndCheck(gl, () => gl.texImage2D(tex2d, 0, internalFormat, width, height, 0, textureFormat, textureType, null));
    }
    else {
        webgl_util.callAndCheck(gl, () => gl
            .texStorage2D(tex2d, 1, internalFormat, width, height));
    }
    webgl_util.callAndCheck(gl, () => gl.bindTexture(gl.TEXTURE_2D, null));
    return { texture, texShape: [height, width] };
}
export function getInternalFormatForFloat32MatrixTexture(textureConfig) {
    return textureConfig.internalFormatFloat;
}
export function createFloat32MatrixTexture(gl, rows, columns, textureConfig) {
    const [width, height] = tex_util.getUnpackedMatrixTextureShapeWidthHeight(rows, columns);
    return createAndConfigureTexture(gl, width, height, getInternalFormatForFloat32MatrixTexture(textureConfig), textureConfig.textureFormatFloat, gl.FLOAT);
}
export function getInternalFormatForFloat16MatrixTexture(textureConfig) {
    return textureConfig.internalFormatHalfFloat;
}
export function createFloat16MatrixTexture(gl, rows, columns, textureConfig) {
    const [width, height] = tex_util.getUnpackedMatrixTextureShapeWidthHeight(rows, columns);
    return createAndConfigureTexture(gl, width, height, getInternalFormatForFloat16MatrixTexture(textureConfig), textureConfig.textureFormatFloat, textureConfig.textureTypeHalfFloat);
}
export function getInternalFormatForUnsignedBytesMatrixTexture(textureConfig) {
    return textureConfig.downloadTextureFormat;
}
export function createUnsignedBytesMatrixTexture(gl, rows, columns, textureConfig) {
    const [width, height] = tex_util.getUnpackedMatrixTextureShapeWidthHeight(rows, columns);
    return createAndConfigureTexture(gl, width, height, getInternalFormatForUnsignedBytesMatrixTexture(textureConfig), gl.RGBA, gl.UNSIGNED_BYTE);
}
export function getInternalFormatForPackedMatrixTexture(textureConfig) {
    return textureConfig.internalFormatPackedFloat;
}
export function createPackedMatrixTexture(gl, rows, columns, textureConfig) {
    const [width, height] = tex_util.getPackedMatrixTextureShapeWidthHeight(rows, columns);
    return createAndConfigureTexture(gl, width, height, getInternalFormatForPackedMatrixTexture(textureConfig), gl.RGBA, gl.FLOAT);
}
export function getInternalFormatForFloat16PackedMatrixTexture(textureConfig) {
    return textureConfig.internalFormatPackedHalfFloat;
}
export function createFloat16PackedMatrixTexture(gl, rows, columns, textureConfig) {
    const [width, height] = tex_util.getPackedMatrixTextureShapeWidthHeight(rows, columns);
    return createAndConfigureTexture(gl, width, height, getInternalFormatForFloat16PackedMatrixTexture(textureConfig), gl.RGBA, textureConfig.textureTypeHalfFloat);
}
export function bindVertexProgramAttributeStreams(gl, program, vertexBuffer) {
    const posOffset = 0; // x is the first buffer element
    const uvOffset = 3 * 4; // uv comes after [x y z]
    const stride = (3 * 4) + (2 * 4); // xyz + uv, each entry is 4-byte float.
    webgl_util.callAndCheck(gl, () => gl.bindBuffer(gl.ARRAY_BUFFER, vertexBuffer));
    const success = webgl_util.bindVertexBufferToProgramAttribute(gl, program, 'clipSpacePos', vertexBuffer, 3, stride, posOffset);
    return success &&
        webgl_util.bindVertexBufferToProgramAttribute(gl, program, 'uv', vertexBuffer, 2, stride, uvOffset);
}
export function uploadDenseMatrixToTexture(gl, texture, width, height, data, textureConfig) {
    webgl_util.callAndCheck(gl, () => gl.bindTexture(gl.TEXTURE_2D, texture));
    let dataForUpload, texelDataType, internalFormat;
    if (data instanceof Uint8Array) {
        dataForUpload = new Uint8Array(width * height * 4);
        texelDataType = gl.UNSIGNED_BYTE;
        internalFormat = gl.RGBA;
    }
    else {
        dataForUpload = new Float32Array(width * height * 4);
        texelDataType = gl.FLOAT;
        internalFormat = textureConfig.internalFormatPackedFloat;
    }
    dataForUpload.set(data);
    if (env().getNumber('WEBGL_VERSION') === 2) {
        webgl_util.callAndCheck(gl, () => gl.texSubImage2D(gl.TEXTURE_2D, 0, 0, 0, width, height, gl.RGBA, texelDataType, dataForUpload));
    }
    else {
        webgl_util.callAndCheck(gl, () => gl.texImage2D(gl.TEXTURE_2D, 0, internalFormat, width, height, 0, gl.RGBA, texelDataType, dataForUpload));
    }
    webgl_util.callAndCheck(gl, () => gl.bindTexture(gl.TEXTURE_2D, null));
}
export function uploadPixelDataToTexture(gl, texture, pixels) {
    webgl_util.callAndCheck(gl, () => gl.bindTexture(gl.TEXTURE_2D, texture));
    if (pixels.data instanceof Uint8Array) {
        if (env().getNumber('WEBGL_VERSION') === 2) {
            webgl_util.callAndCheck(gl, () => gl.texSubImage2D(gl.TEXTURE_2D, 0, 0, 0, pixels.width, pixels.height, gl.RGBA, gl.UNSIGNED_BYTE, pixels.data));
        }
        else {
            webgl_util.callAndCheck(gl, () => gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, pixels.width, pixels.height, 0, gl.RGBA, gl.UNSIGNED_BYTE, pixels.data));
        }
    }
    else {
        if (env().getNumber('WEBGL_VERSION') === 2) {
            webgl_util.callAndCheck(gl, () => gl.texSubImage2D(gl.TEXTURE_2D, 0, 0, 0, gl.RGBA, gl.UNSIGNED_BYTE, pixels));
        }
        else {
            webgl_util.callAndCheck(gl, () => gl.texImage2D(gl.TEXTURE_2D, 0, gl.RGBA, gl.RGBA, gl.UNSIGNED_BYTE, pixels));
        }
    }
    webgl_util.callAndCheck(gl, () => gl.bindTexture(gl.TEXTURE_2D, null));
}
export function createBufferFromOutputTexture(gl2, rows, columns, textureConfig) {
    // Create and bind the buffer.
    const buffer = gl2.createBuffer();
    webgl_util.callAndCheck(gl2, () => gl2.bindBuffer(gl2.PIXEL_PACK_BUFFER, buffer));
    // Initialize the buffer to the size of the texture in bytes.
    const bytesPerFloat = 4;
    const valuesPerTexel = 4;
    const bufferSizeBytes = bytesPerFloat * valuesPerTexel * rows * columns;
    webgl_util.callAndCheck(gl2, () => gl2.bufferData(gl2.PIXEL_PACK_BUFFER, bufferSizeBytes, gl2.STREAM_READ));
    // Enqueue a command on the GPU command queue to copy of texture into the
    // buffer.
    webgl_util.callAndCheck(gl2, () => gl2.readPixels(0, 0, columns, rows, gl2.RGBA, gl2.FLOAT, 0));
    webgl_util.callAndCheck(gl2, () => gl2.bindBuffer(gl2.PIXEL_PACK_BUFFER, null));
    return buffer;
}
export function downloadFloat32MatrixFromBuffer(gl, buffer, size) {
    const gl2 = gl;
    const downloadTarget = new Float32Array(size);
    gl2.bindBuffer(gl2.PIXEL_PACK_BUFFER, buffer);
    gl2.getBufferSubData(gl2.PIXEL_PACK_BUFFER, 0, downloadTarget);
    gl2.bindBuffer(gl2.PIXEL_PACK_BUFFER, null);
    return downloadTarget;
}
export function downloadByteEncodedFloatMatrixFromOutputTexture(gl, rows, columns, textureConfig) {
    const [w, h] = tex_util.getUnpackedMatrixTextureShapeWidthHeight(rows, columns);
    const numChannels = 4;
    const downloadTarget = new Uint8Array(tex_util.getUnpackedArraySizeFromMatrixSize(rows * columns, numChannels));
    webgl_util.callAndCheck(gl, () => gl.readPixels(0, 0, w, h, textureConfig.downloadTextureFormat, gl.UNSIGNED_BYTE, downloadTarget));
    // By wrapping the buffer in a Float32Array, we use native browser IEEE 754
    // decoding of the 4 bytes that back each 32 bit float.
    return new Float32Array(downloadTarget.buffer);
}
export function downloadPackedMatrixFromBuffer(gl, buffer, batch, rows, cols, physicalRows, physicalCols, textureConfig) {
    const gl2 = gl;
    const downloadTarget = new Float32Array(tex_util.getPackedRGBAArraySizeFromMatrixShape(physicalRows, physicalCols));
    gl2.bindBuffer(gl2.PIXEL_PACK_BUFFER, buffer);
    gl2.getBufferSubData(gl2.PIXEL_PACK_BUFFER, 0, downloadTarget);
    gl2.bindBuffer(gl2.PIXEL_PACK_BUFFER, null);
    return downloadTarget;
}
export function downloadMatrixFromPackedOutputTexture(gl, physicalRows, physicalCols) {
    const packedRGBA = new Float32Array(physicalRows * physicalCols * 4);
    webgl_util.callAndCheck(gl, () => gl.readPixels(0, 0, physicalCols, physicalRows, gl.RGBA, gl.FLOAT, packedRGBA));
    return packedRGBA;
}
//# sourceMappingURL=data:application/json;base64,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