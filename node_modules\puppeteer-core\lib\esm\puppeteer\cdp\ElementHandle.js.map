{"version": 3, "file": "ElementHandle.js", "sourceRoot": "", "sources": ["../../../../src/cdp/ElementHandle.ts"], "names": [], "mappings": "AAAA;;;;GAIG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOH,OAAO,EAAC,aAAa,EAAoB,MAAM,yBAAyB,CAAC;AACzE,OAAO,EAAC,UAAU,EAAC,MAAM,mBAAmB,CAAC;AAC7C,OAAO,EAAC,MAAM,EAAC,MAAM,mBAAmB,CAAC;AACzC,OAAO,EAAC,eAAe,EAAC,MAAM,uBAAuB,CAAC;AAKtD,OAAO,EAAC,WAAW,EAAC,MAAM,eAAe,CAAC;AAE1C;;;;;;GAMG;IACU,gBAAgB;;sBAEnB,aAAa;;;;;;iBAFV,gBAEX,SAAQ,WAA0B;;;wCAkCjC,eAAe,EAAE;0CAWjB,eAAe,EAAE,EACjB,CAAA,KAAA,aAAa,CAAA,CAAC,kBAAkB;sCAgBhC,eAAe,EAAE,EACjB,CAAA,KAAA,aAAa,CAAA,CAAC,kBAAkB;oCAiEhC,eAAe,EAAE;YA7FlB,uLAAe,YAAY,6DAQ1B;YAID,6LAAe,cAAc,6DAa5B;YAID,iLAAe,UAAU,6DA8DxB;YAGD,2KAAe,QAAQ,6DAWtB;;;QAzID,YACE,KAAoB,EACpB,YAA2C;YAE3C,KAAK,CAAC,IAAI,WAAW,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC,CAAC;;SAC7C;QAED,IAAa,KAAK;YAChB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;QAC3B,CAAC;QAED,IAAI,MAAM;YACR,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC;QAC5B,CAAC;QAEQ,YAAY;YACnB,OAAO,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;QACpC,CAAC;QAED,IAAI,aAAa;YACf,OAAO,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC;QAClC,CAAC;QAED,IAAa,KAAK;YAChB,OAAO,IAAI,CAAC,KAAK,CAAC,WAAuB,CAAC;QAC5C,CAAC;QAOQ,KAAK,CAAC,YAAY;YACzB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBAC1D,QAAQ,EAAE,IAAI,CAAC,EAAE;aAClB,CAAC,CAAC;YACH,IAAI,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;gBAC9C,OAAO,IAAI,CAAC;YACd,CAAC;YACD,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACzD,CAAC;QAIQ,KAAK,CAAC,cAAc;YAG3B,MAAM,IAAI,CAAC,sBAAsB,EAAE,CAAC;YACpC,IAAI,CAAC;gBACH,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;oBACnD,QAAQ,EAAE,IAAI,CAAC,EAAE;iBAClB,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,UAAU,CAAC,KAAK,CAAC,CAAC;gBAClB,oFAAoF;gBACpF,MAAM,KAAK,CAAC,cAAc,EAAE,CAAC;YAC/B,CAAC;QACH,CAAC;QAIQ,KAAK,CAAC,UAAU,CAEvB,GAAG,SAAmB;YAEtB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;gBAC/C,OAAO,OAAO,CAAC,QAAQ,CAAC;YAC1B,CAAC,CAAC,CAAC;YACH,MAAM,CACJ,SAAS,CAAC,MAAM,IAAI,CAAC,IAAI,UAAU,EACnC,iEAAiE,CAClE,CAAC;YAEF,gDAAgD;YAChD,IAAI,IAAiB,CAAC;YACtB,IAAI,CAAC;gBACH,IAAI,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,CAAC;YAC9B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,KAAK,YAAY,SAAS,EAAE,CAAC;oBAC/B,MAAM,IAAI,KAAK,CACb,iEAAiE,CAClE,CAAC;gBACJ,CAAC;gBACD,MAAM,KAAK,CAAC;YACd,CAAC;YACD,MAAM,KAAK,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;gBACrC,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE,CAAC;oBACvE,OAAO,QAAQ,CAAC;gBAClB,CAAC;qBAAM,CAAC;oBACN,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAChC,CAAC;YACH,CAAC,CAAC,CAAC;YAEH;;;;eAIG;YACH,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACvB,wEAAwE;gBACxE,8BAA8B;gBAC9B,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;oBAC5B,OAAO,CAAC,KAAK,GAAG,IAAI,YAAY,EAAE,CAAC,KAAK,CAAC;oBAEzC,gFAAgF;oBAChF,OAAO,CAAC,aAAa,CACnB,IAAI,KAAK,CAAC,OAAO,EAAE,EAAC,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAC,CAAC,CACpD,CAAC;oBACF,OAAO,CAAC,aAAa,CAAC,IAAI,KAAK,CAAC,QAAQ,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;gBAC9D,CAAC,CAAC,CAAC;gBACH,OAAO;YACT,CAAC;YAED,MAAM,EACJ,IAAI,EAAE,EAAC,aAAa,EAAC,GACtB,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBAC7C,QAAQ,EAAE,IAAI,CAAC,EAAE;aAClB,CAAC,CAAC;YACH,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB,EAAE;gBAC9C,QAAQ,EAAE,IAAI,CAAC,EAAE;gBACjB,KAAK;gBACL,aAAa;aACd,CAAC,CAAC;QACL,CAAC;QAGQ,KAAK,CAAC,QAAQ,CAAC,IAAkB;YACxC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBAC1D,QAAQ,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE;aACzB,CAAC,CAAC;YACH,MAAM,OAAO,GAAG,QAAQ,CAAC,IAAI,CAAC,aAAa,CAAC;YAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YAC/B,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,EAAE;gBACzC,OAAO;gBACP,OAAO;gBACP,IAAI,EAAE,IAAI,CAAC,UAAU;aACtB,CAAC,CAAC;QACL,CAAC;;;SA9IU,gBAAgB"}