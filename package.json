{"name": "quotex-smart-trading-bot", "version": "1.0.0", "description": "نظام تداول ذكي للخيارات الثنائية مع استراتيجية سكالبينغ متقدمة - Smart Binary Options Trading Bot with Advanced Scalping Strategy", "main": "index.js", "scripts": {"start": "node index.js", "dev": "nodemon index.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint .", "lint:fix": "eslint . --fix", "setup": "npm install && echo 'Smart Trading Bot setup complete! 🚀'"}, "keywords": ["quotex", "binary-options", "trading-bot", "scalping", "technical-analysis", "ai-trading", "websocket", "nodejs", "automated-trading", "machine-learning"], "author": "Smart Trading Systems", "license": "MIT", "dependencies": {"axios": "^1.6.2", "brain.js": "^2.0.0-beta.23", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "eventemitter3": "^5.0.1", "express": "^4.18.2", "helmet": "^7.1.0", "lodash": "^4.17.21", "mathjs": "^12.2.0", "ml-matrix": "^6.10.7", "moment": "^2.29.4", "node-cron": "^3.0.3", "puppeteer": "^21.11.0", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "simple-statistics": "^7.8.3", "sqlite3": "^5.1.6", "uuid": "^9.0.1", "winston": "^3.11.0", "ws": "^8.18.0"}, "devDependencies": {"eslint": "^8.55.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "nodemon": "^3.0.2", "supertest": "^6.3.3"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/smart-trading/quotex-bot.git"}, "bugs": {"url": "https://github.com/smart-trading/quotex-bot/issues"}, "homepage": "https://github.com/smart-trading/quotex-bot#readme"}