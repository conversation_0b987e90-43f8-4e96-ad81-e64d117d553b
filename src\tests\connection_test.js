/**
 * اختبار الاتصال مع منصة Quotex باستخدام Puppeteer
 * Connection Test for Quotex Platform using Puppeteer
 */

const QuotexConnector = require('../core/QuotexConnector');
const DataCollector = require('../layers/DataCollector');
const Logger = require('../utils/Logger');

class ConnectionTest {
  constructor() {
    this.logger = new Logger('ConnectionTest');
    this.connector = null;
    this.dataCollector = null;
  }

  /**
   * تشغيل اختبار الاتصال
   */
  async runTest() {
    this.logger.info('🚀 بدء اختبار الاتصال مع منصة Quotex...');
    
    try {
      // اختبار 1: الاتصال الأساسي
      await this.testBasicConnection();
      
      // اختبار 2: جلب قائمة الأزواج
      await this.testPairsList();
      
      // اختبار 3: جلب البيانات التاريخية
      await this.testHistoricalData();
      
      this.logger.info('✅ جميع الاختبارات نجحت!');
      
    } catch (error) {
      this.logger.error('❌ فشل في الاختبار:', error);
      throw error;
    } finally {
      await this.cleanup();
    }
  }

  /**
   * اختبار الاتصال الأساسي
   */
  async testBasicConnection() {
    this.logger.info('📡 اختبار الاتصال الأساسي...');
    
    this.connector = new QuotexConnector({
      headless: false // لرؤية المتصفح أثناء الاختبار
    });
    
    await this.connector.connect();
    
    if (this.connector.isConnectionActive()) {
      this.logger.info('✅ تم الاتصال بنجاح!');
      
      const connectionInfo = this.connector.getConnectionInfo();
      this.logger.info('معلومات الاتصال:', connectionInfo);
    } else {
      throw new Error('فشل في تأسيس الاتصال');
    }
  }

  /**
   * اختبار جلب قائمة الأزواج
   */
  async testPairsList() {
    this.logger.info('📋 اختبار جلب قائمة الأزواج...');
    
    this.dataCollector = new DataCollector(this.connector);
    
    try {
      const pairs = await this.dataCollector.fetchAvailablePairs();
      
      if (pairs && pairs.length > 0) {
        this.logger.info(`✅ تم جلب ${pairs.length} زوج بنجاح!`);
        
        // عرض أول 3 أزواج كمثال
        this.logger.info('أمثلة على الأزواج المتاحة:');
        pairs.slice(0, 3).forEach(pair => {
          this.logger.info(`  - ${pair.symbol}: ${pair.name}`);
        });
      } else {
        throw new Error('لم يتم جلب أي أزواج');
      }
    } catch (error) {
      this.logger.warn('تحذير: فشل في جلب قائمة الأزواج، ربما تحتاج لتسجيل الدخول');
      // لا نرمي خطأ هنا لأن هذا قد يكون طبيعياً
    }
  }

  /**
   * اختبار جلب البيانات التاريخية
   */
  async testHistoricalData() {
    this.logger.info('📊 اختبار جلب البيانات التاريخية...');
    
    try {
      const testPair = 'EURUSD_otc';
      const candles = await this.dataCollector.fetchPairHistoricalData(testPair);
      
      if (candles && candles.length > 0) {
        this.logger.info(`✅ تم جلب ${candles.length} شمعة للزوج ${testPair}!`);
        
        // عرض آخر شمعة كمثال
        const lastCandle = candles[candles.length - 1];
        this.logger.info('آخر شمعة:', {
          timestamp: new Date(lastCandle.timestamp).toLocaleString(),
          open: lastCandle.open,
          close: lastCandle.close
        });
      } else {
        throw new Error('لم يتم جلب أي بيانات تاريخية');
      }
    } catch (error) {
      this.logger.warn('تحذير: فشل في جلب البيانات التاريخية، ربما تحتاج لتسجيل الدخول');
      // لا نرمي خطأ هنا لأن هذا قد يكون طبيعياً
    }
  }

  /**
   * تنظيف الموارد
   */
  async cleanup() {
    this.logger.info('🧹 تنظيف الموارد...');
    
    try {
      if (this.dataCollector) {
        await this.dataCollector.stopCollection();
      }
      
      if (this.connector) {
        await this.connector.disconnect();
      }
      
      this.logger.info('✅ تم تنظيف الموارد بنجاح');
    } catch (error) {
      this.logger.error('خطأ في تنظيف الموارد:', error);
    }
  }
}

// تشغيل الاختبار إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  async function main() {
    const test = new ConnectionTest();
    
    try {
      await test.runTest();
      process.exit(0);
    } catch (error) {
      console.error('فشل الاختبار:', error);
      process.exit(1);
    }
  }
  
  // التعامل مع الإشارات
  process.on('SIGINT', () => {
    console.log('\n⏹️ تم إيقاف الاختبار بواسطة المستخدم');
    process.exit(0);
  });
  
  main().catch(console.error);
}

module.exports = ConnectionTest;
