{"name": "mathjs", "version": "12.4.3", "description": "Math.js is an extensive math library for JavaScript and Node.js. It features a flexible expression parser with support for symbolic computation, comes with a large set of built-in functions and constants, and offers an integrated solution to work with different data types like numbers, big numbers, complex numbers, fractions, units, and matrices.", "author": "<PERSON><PERSON> <<EMAIL>> (https://github.com/josdejong)", "homepage": "https://mathjs.org", "repository": {"type": "git", "url": "https://github.com/josdejong/mathjs.git"}, "license": "Apache-2.0", "keywords": ["math", "mathematics", "functions", "numeric", "algebra", "parser", "expression", "number", "bignumber", "complex", "fraction", "matrix", "unit"], "dependencies": {"@babel/runtime": "^7.24.4", "complex.js": "^2.1.1", "decimal.js": "^10.4.3", "escape-latex": "^1.2.0", "fraction.js": "4.3.4", "javascript-natural-sort": "^0.7.1", "seedrandom": "^3.0.5", "tiny-emitter": "^2.1.0", "typed-function": "^4.1.1"}, "devDependencies": {"@babel/core": "7.24.4", "@babel/plugin-transform-object-assign": "7.24.1", "@babel/plugin-transform-runtime": "7.24.3", "@babel/preset-env": "7.24.4", "@babel/register": "7.23.7", "@types/assert": "1.5.10", "@types/mocha": "10.0.6", "@typescript-eslint/eslint-plugin": "7.7.1", "@typescript-eslint/parser": "7.7.1", "assert": "2.1.0", "babel-loader": "9.1.3", "benchmark": "2.1.4", "c8": "9.1.0", "codecov": "3.8.3", "core-js": "3.37.0", "del": "6.1.1", "dtslint": "4.2.1", "eslint": "8.57.0", "eslint-config-prettier": "9.1.0", "eslint-config-standard": "17.1.0", "eslint-plugin-import": "2.29.1", "eslint-plugin-mocha": "10.4.3", "eslint-plugin-n": "16.6.2", "eslint-plugin-prettier": "5.1.3", "eslint-plugin-promise": "6.1.1", "expect-type": "0.19.0", "expr-eval": "2.0.2", "fancy-log": "2.0.0", "glob": "8.1.0", "gulp": "5.0.0", "gulp-babel": "8.0.0", "handlebars": "4.7.8", "jsep": "1.3.8", "karma": "6.4.3", "karma-browserstack-launcher": "1.6.0", "karma-firefox-launcher": "2.1.3", "karma-mocha": "2.0.1", "karma-mocha-reporter": "2.2.5", "karma-webpack": "5.0.1", "mkdirp": "3.0.1", "mocha": "10.4.0", "mocha-junit-reporter": "2.2.1", "ndarray": "1.0.19", "ndarray-determinant": "1.0.0", "ndarray-gemm": "1.0.0", "ndarray-ops": "1.2.2", "ndarray-pack": "1.2.1", "numericjs": "1.2.6", "pad-right": "0.2.2", "prettier": "3.2.5", "process": "0.11.10", "sylvester": "0.0.21", "ts-node": "10.9.2", "typescript": "5.4.5", "webpack": "5.91.0", "zeros": "1.0.0"}, "type": "module", "main": "./lib/cjs", "types": "./types/index.d.ts", "module": "./lib/esm", "unpkg": "./lib/browser/math.js", "jsdelivr": "./lib/browser/math.js", "exports": {".": {"types": "./types/index.d.ts", "import": "./lib/esm/index.js", "require": "./lib/cjs/index.js"}, "./number": {"types": "./types/index.d.ts", "import": "./lib/esm/number.js", "require": "./lib/cjs/number.js"}, "./lib/esm/number": {"types": "./types/index.d.ts", "import": "./lib/esm/number.js", "require": "./lib/cjs/number.js"}, "./package.json": "./package.json", "./lib/browser/math.js": "./lib/browser/math.js", "./lib/browser/math.js.map": "./lib/browser/math.js.map", "./dist/math.js": "./dist/math.js", "./dist/math.min.js": "./dist/math.min.js", "./main/es5/index.js": "./main/es5/index.js", "./main/es5/number.js": "./main/es5/number.js", "./main/esm/index.js": "./main/esm/index.js", "./main/esm/number.js": "./main/esm/number.js", "./number.js": "./number.cjs"}, "files": ["bin", "dist", "lib", "main", "types", "number.cjs", "LICENSE", "NOTICE", "README.md", "HISTORY.md", "CONTRIBUTING.md"], "directories": {"bin": "./bin", "lib": "./lib"}, "scripts": {"build": "gulp --gulpfile gulpfile.cjs && npm run update-authors", "build-and-test": "npm run build && npm run test:all && npm run lint", "build:clean": "gulp --gulpfile gulpfile.cjs clean", "build:docs": "gulp --gulpfile gulpfile.cjs docs", "compile": "gulp --gulpfile gulpfile.cjs compile", "watch": "gulp --gulpfile gulpfile.cjs watch", "lint": "eslint --cache --max-warnings 0 src/ test/ types/", "format": "npm run lint -- --fix", "validate:ascii": "gulp --gulpfile gulpfile.cjs validate:ascii", "test": "npm run test:src && npm run lint", "test:src": "mocha test/unit-tests", "test:generated": "mocha test/generated-code-tests", "test:node": "mocha test/node-tests/*.test.js test/node-tests/**/*.test.js", "test:all": "npm run test:src && npm run test:generated && npm run test:node && npm run test:types", "test:browser": "karma start test/browser-test-config/local-karma.js", "test:browserstack": "karma start test/browser-test-config/browserstack-karma.js", "test:types": " tsc -p ./tsconfig.json && node --loader ts-node/esm ./test/typescript-tests/testTypes.ts", "coverage": "c8 --reporter=lcov --reporter=text-summary mocha test/unit-tests && echo \"\nDetailed coverage report is available at ./coverage/lcov-report/index.html\"", "prepublishOnly": "npm run test:all && npm run lint", "update-authors": "node ./tools/update-authors.js"}, "bin": {"mathjs": "./bin/cli.js"}, "engines": {"node": ">= 18"}, "bugs": {"url": "https://github.com/josdejong/mathjs/issues"}, "sideEffects": false}