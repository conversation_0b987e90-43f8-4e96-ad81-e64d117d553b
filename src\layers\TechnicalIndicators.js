/**
 * طبقة المؤشرات الفنية - Technical Indicators Layer
 * مسؤولة عن حساب جميع المؤشرات الفنية المطلوبة للاستراتيجية
 */

const EventEmitter = require('events');
const config = require('../config/config');
const Logger = require('../utils/Logger');

class TechnicalIndicators extends EventEmitter {
  constructor() {
    super();
    
    this.logger = new Logger('TechnicalIndicators');
    this.options = config.indicators;
    
    // تخزين النتائج المحسوبة
    this.indicators = new Map(); // pair -> indicators object
  }

  /**
   * حساب جميع المؤشرات لزوج معين
   */
  calculateIndicators(pair, candles) {
    try {
      if (!candles || candles.length < 50) {
        throw new Error(`بيانات غير كافية للزوج ${pair}`);
      }

      const indicators = {
        timestamp: Date.now(),
        pair,
        ema: this.calculateEMA(candles),
        rsi: this.calculateRSI(candles),
        bollinger: this.calculateBollingerBands(candles),
        atr: this.calculateATR(candles),
        momentum: this.calculateMomentum(candles),
        heikenAshi: this.calculateHeikenAshi(candles),
        sma: this.calculateSMA(candles),
        macd: this.calculateMACD(candles)
      };

      // حفظ النتائج
      this.indicators.set(pair, indicators);
      
      // إرسال إشعار بالتحديث
      this.emit('indicatorsUpdated', {
        pair,
        indicators
      });

      this.logger.debug(`تم حساب المؤشرات للزوج ${pair}`);
      
      return indicators;
      
    } catch (error) {
      this.logger.error(`خطأ في حساب المؤشرات للزوج ${pair}:`, error);
      throw error;
    }
  }

  /**
   * حساب المتوسط المتحرك الأسي (EMA)
   */
  calculateEMA(candles) {
    const fast = this.options.ema.fast;
    const slow = this.options.ema.slow;
    
    const emaFast = this.calculateEMAValues(candles, fast);
    const emaSlow = this.calculateEMAValues(candles, slow);
    
    const current = {
      fast: emaFast[emaFast.length - 1],
      slow: emaSlow[emaSlow.length - 1]
    };
    
    const previous = {
      fast: emaFast[emaFast.length - 2],
      slow: emaSlow[emaSlow.length - 2]
    };
    
    return {
      fast: current.fast,
      slow: current.slow,
      crossover: this.detectCrossover(current, previous),
      trend: current.fast > current.slow ? 'bullish' : 'bearish',
      strength: Math.abs(current.fast - current.slow) / current.slow * 100
    };
  }

  /**
   * حساب قيم EMA
   */
  calculateEMAValues(candles, period) {
    const prices = candles.map(c => c.close);
    const ema = [];
    const multiplier = 2 / (period + 1);
    
    // أول قيمة هي SMA
    let sum = 0;
    for (let i = 0; i < period; i++) {
      sum += prices[i];
    }
    ema[period - 1] = sum / period;
    
    // باقي القيم
    for (let i = period; i < prices.length; i++) {
      ema[i] = (prices[i] * multiplier) + (ema[i - 1] * (1 - multiplier));
    }
    
    return ema.filter(val => val !== undefined);
  }

  /**
   * حساب مؤشر القوة النسبية (RSI)
   */
  calculateRSI(candles) {
    const period = this.options.rsi.period;
    const prices = candles.map(c => c.close);
    
    if (prices.length < period + 1) {
      return { value: 50, signal: 'neutral' };
    }
    
    let gains = 0;
    let losses = 0;
    
    // حساب المتوسط الأولي
    for (let i = 1; i <= period; i++) {
      const change = prices[i] - prices[i - 1];
      if (change > 0) {
        gains += change;
      } else {
        losses += Math.abs(change);
      }
    }
    
    let avgGain = gains / period;
    let avgLoss = losses / period;
    
    // حساب RSI للفترات المتبقية
    for (let i = period + 1; i < prices.length; i++) {
      const change = prices[i] - prices[i - 1];
      const gain = change > 0 ? change : 0;
      const loss = change < 0 ? Math.abs(change) : 0;
      
      avgGain = ((avgGain * (period - 1)) + gain) / period;
      avgLoss = ((avgLoss * (period - 1)) + loss) / period;
    }
    
    const rs = avgGain / avgLoss;
    const rsi = 100 - (100 / (1 + rs));
    
    let signal = 'neutral';
    if (rsi > this.options.rsi.overbought) {
      signal = 'overbought';
    } else if (rsi < this.options.rsi.oversold) {
      signal = 'oversold';
    }
    
    return {
      value: rsi,
      signal,
      divergence: this.detectRSIDivergence(candles, rsi)
    };
  }

  /**
   * حساب بولينجر باندز
   */
  calculateBollingerBands(candles) {
    const period = this.options.bollinger.period;
    const deviation = this.options.bollinger.deviation;
    
    if (candles.length < period) {
      return null;
    }
    
    const prices = candles.slice(-period).map(c => c.close);
    const sma = prices.reduce((sum, price) => sum + price, 0) / period;
    
    // حساب الانحراف المعياري
    const variance = prices.reduce((sum, price) => sum + Math.pow(price - sma, 2), 0) / period;
    const stdDev = Math.sqrt(variance);
    
    const upperBand = sma + (stdDev * deviation);
    const lowerBand = sma - (stdDev * deviation);
    const currentPrice = candles[candles.length - 1].close;
    
    // تحديد موقع السعر
    let position = 'middle';
    if (currentPrice > upperBand) {
      position = 'above_upper';
    } else if (currentPrice < lowerBand) {
      position = 'below_lower';
    } else if (currentPrice > sma) {
      position = 'upper_half';
    } else {
      position = 'lower_half';
    }
    
    return {
      upper: upperBand,
      middle: sma,
      lower: lowerBand,
      position,
      squeeze: (upperBand - lowerBand) / sma < 0.1, // ضغط البولينجر
      bandwidth: (upperBand - lowerBand) / sma * 100
    };
  }

  /**
   * حساب متوسط المدى الحقيقي (ATR)
   */
  calculateATR(candles) {
    const period = this.options.atr.period;
    
    if (candles.length < period + 1) {
      return { value: 0, volatility: 'low' };
    }
    
    const trueRanges = [];
    
    for (let i = 1; i < candles.length; i++) {
      const current = candles[i];
      const previous = candles[i - 1];
      
      const tr1 = current.high - current.low;
      const tr2 = Math.abs(current.high - previous.close);
      const tr3 = Math.abs(current.low - previous.close);
      
      trueRanges.push(Math.max(tr1, tr2, tr3));
    }
    
    // حساب ATR كمتوسط للفترة المحددة
    const recentTR = trueRanges.slice(-period);
    const atr = recentTR.reduce((sum, tr) => sum + tr, 0) / period;
    
    // تحديد مستوى التقلب
    const currentPrice = candles[candles.length - 1].close;
    const atrPercentage = (atr / currentPrice) * 100;
    
    let volatility = 'low';
    if (atrPercentage > 2) {
      volatility = 'high';
    } else if (atrPercentage > 1) {
      volatility = 'medium';
    }
    
    return {
      value: atr,
      percentage: atrPercentage,
      volatility
    };
  }

  /**
   * حساب الزخم (Momentum)
   */
  calculateMomentum(candles) {
    const period = this.options.momentum.period;
    
    if (candles.length < period + 1) {
      return { value: 0, direction: 'neutral' };
    }
    
    const currentPrice = candles[candles.length - 1].close;
    const pastPrice = candles[candles.length - 1 - period].close;
    
    const momentum = ((currentPrice - pastPrice) / pastPrice) * 100;
    
    let direction = 'neutral';
    if (momentum > 0.5) {
      direction = 'bullish';
    } else if (momentum < -0.5) {
      direction = 'bearish';
    }
    
    return {
      value: momentum,
      direction,
      strength: Math.abs(momentum)
    };
  }

  /**
   * حساب هيكن آشي
   */
  calculateHeikenAshi(candles) {
    if (candles.length < 2) {
      return null;
    }
    
    const heikenAshi = [];
    
    // أول شمعة هيكن آشي
    let haOpen = (candles[0].open + candles[0].close) / 2;
    let haClose = (candles[0].open + candles[0].high + candles[0].low + candles[0].close) / 4;
    
    heikenAshi.push({
      open: haOpen,
      close: haClose,
      high: candles[0].high,
      low: candles[0].low
    });
    
    // باقي الشموع
    for (let i = 1; i < candles.length; i++) {
      const candle = candles[i];
      
      haClose = (candle.open + candle.high + candle.low + candle.close) / 4;
      haOpen = (heikenAshi[i - 1].open + heikenAshi[i - 1].close) / 2;
      const haHigh = Math.max(candle.high, haOpen, haClose);
      const haLow = Math.min(candle.low, haOpen, haClose);
      
      heikenAshi.push({
        open: haOpen,
        close: haClose,
        high: haHigh,
        low: haLow
      });
    }
    
    const lastHA = heikenAshi[heikenAshi.length - 1];
    const prevHA = heikenAshi[heikenAshi.length - 2];
    
    return {
      current: lastHA,
      trend: lastHA.close > lastHA.open ? 'bullish' : 'bearish',
      strength: Math.abs(lastHA.close - lastHA.open) / lastHA.open * 100,
      continuation: (lastHA.close > lastHA.open) === (prevHA.close > prevHA.open)
    };
  }

  /**
   * حساب المتوسط المتحرك البسيط (SMA)
   */
  calculateSMA(candles, period = 20) {
    if (candles.length < period) {
      return null;
    }
    
    const prices = candles.slice(-period).map(c => c.close);
    const sma = prices.reduce((sum, price) => sum + price, 0) / period;
    
    return {
      value: sma,
      trend: candles[candles.length - 1].close > sma ? 'bullish' : 'bearish'
    };
  }

  /**
   * حساب MACD
   */
  calculateMACD(candles) {
    const ema12 = this.calculateEMAValues(candles, 12);
    const ema26 = this.calculateEMAValues(candles, 26);
    
    if (ema12.length < 26 || ema26.length < 26) {
      return null;
    }
    
    const macdLine = ema12[ema12.length - 1] - ema26[ema26.length - 1];
    const signalLine = this.calculateEMAValues([{close: macdLine}], 9)[0] || macdLine;
    const histogram = macdLine - signalLine;
    
    return {
      macd: macdLine,
      signal: signalLine,
      histogram,
      crossover: macdLine > signalLine ? 'bullish' : 'bearish'
    };
  }

  /**
   * كشف تقاطع المتوسطات
   */
  detectCrossover(current, previous) {
    if (!previous) return 'none';
    
    if (previous.fast <= previous.slow && current.fast > current.slow) {
      return 'golden'; // تقاطع ذهبي
    } else if (previous.fast >= previous.slow && current.fast < current.slow) {
      return 'death'; // تقاطع الموت
    }
    
    return 'none';
  }

  /**
   * كشف تباعد RSI
   */
  detectRSIDivergence(candles, currentRSI) {
    // تنفيذ مبسط لكشف التباعد
    if (candles.length < 20) return false;
    
    const recentCandles = candles.slice(-20);
    const recentHighs = recentCandles.map(c => c.high);
    const recentLows = recentCandles.map(c => c.low);
    
    const priceHigh = Math.max(...recentHighs);
    const priceLow = Math.min(...recentLows);
    
    // منطق مبسط للتباعد
    return false; // سيتم تطويره لاحقاً
  }

  /**
   * الحصول على المؤشرات لزوج معين
   */
  getIndicators(pair) {
    return this.indicators.get(pair);
  }

  /**
   * تحليل الإشارات الفنية
   */
  analyzeSignals(pair) {
    const indicators = this.getIndicators(pair);
    if (!indicators) return null;

    const signals = {
      bullish: 0,
      bearish: 0,
      neutral: 0,
      strength: 0
    };

    // تحليل EMA
    if (indicators.ema.crossover === 'golden') {
      signals.bullish += 2;
    } else if (indicators.ema.crossover === 'death') {
      signals.bearish += 2;
    } else if (indicators.ema.trend === 'bullish') {
      signals.bullish += 1;
    } else if (indicators.ema.trend === 'bearish') {
      signals.bearish += 1;
    }

    // تحليل RSI
    if (indicators.rsi.signal === 'oversold') {
      signals.bullish += 1;
    } else if (indicators.rsi.signal === 'overbought') {
      signals.bearish += 1;
    }

    // تحليل Bollinger Bands
    if (indicators.bollinger) {
      if (indicators.bollinger.position === 'below_lower') {
        signals.bullish += 1;
      } else if (indicators.bollinger.position === 'above_upper') {
        signals.bearish += 1;
      }
    }

    // حساب القوة الإجمالية
    const total = signals.bullish + signals.bearish + signals.neutral;
    if (total > 0) {
      if (signals.bullish > signals.bearish) {
        signals.strength = (signals.bullish / total) * 100;
      } else {
        signals.strength = (signals.bearish / total) * 100;
      }
    }

    return signals;
  }
}

module.exports = TechnicalIndicators;
