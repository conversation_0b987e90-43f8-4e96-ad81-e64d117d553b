
const QuotexAPI = require(\"../src\");

const SESSION_ID = \"42[\\\"authorization\\\",{\\\"session\\\":\\\"YOUR_SESSION_ID_HERE\\\",\\\"isDemo\\\":1,\\\"tournamentId\\\":0}]\"; // Replace with your actual session ID

async function main() {
    const quotex = new QuotexAPI(SESSION_ID);

    // Event handlers for incoming data
    quotex.onInstrumentsList = (data) => {
        console.log(\"Received Instruments List:\", data);
    };

    quotex.onMarketData = (data) => {
        console.log(\"Received Market Data (Time Sync):\", data);
    };

    quotex.onHistoricalCandles = (data) => {
        console.log(\"Received Historical Candles:\", data);
    };

    quotex.onAccountBalance = (data) => {
        console.log(\"Received Account Balance:\", data);
    };

    quotex.onProfitPercentage = (data) => {
        console.log(\"Received Profit Percentage:\", data);
    };

    quotex.onTradeHistory = (data) => {
        console.log(\"Received Trade History:\", data);
    };

    quotex.onTradeResult = (data) => {
        console.log(\"Trade Result:\", data);
    };

    // General event handlers
    quotex.on(\"authenticated\", () => {
        console.log(\"API Authenticated!\");
    });

    quotex.on(\"disconnected\", (code, reason) => {
        console.log(`API Disconnected: Code ${code}, Reason: ${reason}`);
    });

    quotex.on(\"error\", (error) => {
        console.error(\"API Error:\", error.message);
    });

    try {
        await quotex.connect();
        console.log(\"Connected and authenticated to Quotex.\");

        // Request data using async/await
        const instruments = await quotex.getInstrumentsList();
        console.log(\"Instruments fetched via async/await:\", instruments);

        // Subscribe to market data (handled by onMarketData callback)
        quotex.getMarketData(36); // Example: Subscribe to AUDCAD market data

        const historicalCandles = await quotex.getHistoricalCandles(36, 60, 100); // Example: Request 100 candles for AUDCAD with 60s timeframe
        console.log(\"Historical Candles fetched via async/await:\", historicalCandles);

        const accountBalance = await quotex.getAccountBalance();
        console.log(\"Account Balance fetched via async/await:\", accountBalance);

        const profitPercentage = await quotex.getProfitPercentage(36); // Example: Request profit percentage for AUDCAD
        console.log(\"Profit Percentage fetched via async/await:\", profitPercentage);

        const tradeHistory = await quotex.getTradeHistory();
        console.log(\"Trade History fetched via async/await:\", tradeHistory);

        // Example of placing a trade (uncomment to test)
        // const tradeResult = await quotex.placeTrade(36, 10, \"call\", 60); // Example: instrumentId 36, amount 10, \"call\" (buy), 60 seconds
        // console.log(\"Trade Result via async/await:\", tradeResult);

    } catch (error) {
        console.error(\"Failed to connect or perform operations:\", error);
    }
}

main();


