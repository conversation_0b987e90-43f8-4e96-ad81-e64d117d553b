/**
 * ملف التكوين الرئيسي للنظام
 * Main Configuration File for Smart Trading System
 */

require('dotenv').config();

const config = {
  // إعدادات الاتصال مع Quotex
  quotex: {
    wsUrl: 'https://ws.qxbroker.com',
    apiUrl: 'https://qxbroker.com',
    timeout: 30000,
    reconnectInterval: 5000,
    maxReconnectAttempts: 10,
    socketOptions: {
      transports: ['websocket'],
      upgrade: true,
      rememberUpgrade: true,
      timeout: 30000,
      forceNew: true
    }
  },

  // إعدادات التداول
  trading: {
    defaultAmount: 10,
    maxConcurrentTrades: 3,
    timeframes: ['1m', '5m'],
    defaultTimeframe: '1m',
    expirationTime: 60, // بالثواني
    minConfidenceLevel: 80,
    maxDailyTrades: 50,
    stopLossPercentage: 70 // إيقاف التداول إذا انخفض الرصيد بهذه النسبة
  },

  // إعدادات المؤشرات الفنية
  indicators: {
    ema: {
      fast: 5,
      slow: 10
    },
    rsi: {
      period: 5,
      overbought: 70,
      oversold: 30
    },
    bollinger: {
      period: 20,
      deviation: 2
    },
    atr: {
      period: 5
    },
    momentum: {
      period: 10
    }
  },

  // إعدادات طبقة التحليل الكمي
  quantitative: {
    zScoreThreshold: 2,
    probabilisticFilterThreshold: 70,
    sharpeRatioMinimum: 1.5,
    volatilityRange: {
      min: 0.001,
      max: 0.05
    }
  },

  // إعدادات الذكاء الاصطناعي
  ai: {
    modelType: 'hybrid', // 'xgboost', 'lstm', 'random_forest', 'hybrid'
    confidenceThreshold: 80,
    trainingDataSize: 1000,
    retrainInterval: 24 * 60 * 60 * 1000, // 24 ساعة
    features: [
      'ema_cross',
      'rsi_value',
      'bollinger_position',
      'momentum',
      'atr_normalized',
      'volume_ratio',
      'price_change',
      'candle_pattern'
    ]
  },

  // إعدادات البيانات
  data: {
    historicalCandlesCount: 100,
    maxStorageSize: 10000, // عدد الشموع المحفوظة
    dataRetentionDays: 30,
    backupInterval: 60 * 60 * 1000 // ساعة واحدة
  },

  // إعدادات السجلات
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    maxFiles: 10,
    maxSize: '10m',
    datePattern: 'YYYY-MM-DD',
    logToConsole: true,
    logToFile: true
  },

  // إعدادات الأمان
  security: {
    maxLoginAttempts: 3,
    sessionTimeout: 30 * 60 * 1000, // 30 دقيقة
    encryptionKey: process.env.ENCRYPTION_KEY || 'default-key-change-in-production'
  },

  // إعدادات الأداء
  performance: {
    maxMemoryUsage: 512 * 1024 * 1024, // 512MB
    gcInterval: 5 * 60 * 1000, // 5 دقائق
    monitoringInterval: 30 * 1000 // 30 ثانية
  },

  // إعدادات التطوير
  development: {
    enableDebugMode: process.env.NODE_ENV === 'development',
    mockTrading: process.env.MOCK_TRADING === 'true',
    testDataPath: './data/test_data.json'
  },

  // الأزواج المستهدفة
  targetPairs: [
    'EURUSD_otc',
    'GBPUSD_otc',
    'USDJPY_otc',
    'AUDUSD_otc',
    'USDCAD_otc',
    'EURJPY_otc',
    'GBPJPY_otc',
    'BTCUSD',
    'ETHUSD',
    'LTCUSD'
  ],

  // فترات منع التداول
  tradingRestrictions: {
    // منع التداول في أول وآخر 5 دقائق من كل ساعة
    hourlyRestrictions: {
      startMinutes: [0, 1, 2, 3, 4],
      endMinutes: [55, 56, 57, 58, 59]
    },
    // منع التداول أثناء الأخبار المهمة
    newsBlackout: true,
    // أيام منع التداول
    excludedDays: [] // 0 = الأحد, 6 = السبت
  }
};

module.exports = config;
