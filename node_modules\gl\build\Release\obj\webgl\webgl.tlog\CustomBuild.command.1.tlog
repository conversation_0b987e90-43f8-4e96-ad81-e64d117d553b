^C:\USERS\<USER>\DOWNLOADS\QUOTEX-WS-API (1)\NODE_MODULES\GL\DEPS\WINDOWS\DLL\X64\LIBEGL.DLL
call mkdir "C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl\build\Release" 2>nul & set ERRORLEVEL=0 & copy /Y "C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl\deps\windows\dll\x64\libEGL.dll" "C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl\build\Release\libEGL.dll"
if %errorlevel% neq 0 exit /b %errorlevel%
^C:\USERS\<USER>\DOWNLOADS\QUOTEX-WS-API (1)\NODE_MODULES\GL\DEPS\WINDOWS\DLL\X64\LIBGLESV2.DLL
call mkdir "C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl\build\Release" 2>nul & set ERRORLEVEL=0 & copy /Y "C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl\deps\windows\dll\x64\libGLESv2.dll" "C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl\build\Release\libGLESv2.dll"
if %errorlevel% neq 0 exit /b %errorlevel%
^C:\USERS\<USER>\DOWNLOADS\QUOTEX-WS-API (1)\NODE_MODULES\GL\DEPS\WINDOWS\DLL\X64\D3DCOMPILER_47.DLL
call mkdir "C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl\build\Release" 2>nul & set ERRORLEVEL=0 & copy /Y "C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl\deps\windows\dll\x64\d3dcompiler_47.dll" "C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl\build\Release\d3dcompiler_47.dll"
if %errorlevel% neq 0 exit /b %errorlevel%
