/**
 * كلاس الاتصال الأساسي مع منصة Quotex
 * Core WebSocket Connector for Quotex Platform
 */

const EventEmitter = require('events');
const { io } = require('socket.io-client');
const config = require('../config/config');
const Logger = require('../utils/Logger');

class QuotexConnector extends EventEmitter {
  constructor(options = {}) {
    super();
    
    this.options = {
      ...config.quotex,
      ...options
    };
    
    this.socket = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.sessionId = null;
    this.heartbeatInterval = null;
    this.requestId = 1;
    this.pendingRequests = new Map();
    
    this.logger = new Logger('QuotexConnector');
    
    // ربط الأحداث
    this.setupEventHandlers();
  }

  /**
   * إعداد معالجات الأحداث
   */
  setupEventHandlers() {
    this.on('connected', () => {
      this.logger.info('تم الاتصال بنجاح مع منصة Quotex');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.startHeartbeat();
    });

    this.on('disconnected', () => {
      this.logger.warn('تم قطع الاتصال مع منصة Quotex');
      this.isConnected = false;
      this.stopHeartbeat();
      this.handleReconnection();
    });

    this.on('error', (error) => {
      this.logger.error('خطأ في الاتصال:', error);
    });
  }

  /**
   * الاتصال بمنصة Quotex
   */
  async connect() {
    try {
      this.logger.info('محاولة الاتصال بمنصة Quotex...');

      this.socket = io(this.options.wsUrl, {
        ...this.options.socketOptions,
        extraHeaders: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Origin': 'https://qxbroker.com'
        }
      });

      this.socket.on('connect', () => {
        this.emit('connected');
      });

      this.socket.on('message', (data) => {
        this.handleMessage(data);
      });

      this.socket.on('disconnect', (reason) => {
        this.logger.warn(`تم إغلاق الاتصال - السبب: ${reason}`);
        this.emit('disconnected');
      });

      this.socket.on('connect_error', (error) => {
        this.emit('error', error);
      });

      // الاستماع لجميع الأحداث
      this.socket.onAny((eventName, ...args) => {
        this.handleSocketEvent(eventName, args);
      });

      // انتظار الاتصال
      await this.waitForConnection();

      return true;
    } catch (error) {
      this.logger.error('فشل في الاتصال:', error);
      throw error;
    }
  }

  /**
   * انتظار تأسيس الاتصال
   */
  waitForConnection() {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('انتهت مهلة الاتصال'));
      }, this.options.timeout);

      this.once('connected', () => {
        clearTimeout(timeout);
        resolve();
      });

      this.once('error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });
    });
  }

  /**
   * معالجة أحداث Socket.IO
   */
  handleSocketEvent(eventName, args) {
    try {
      this.logger.debug(`حدث Socket.IO: ${eventName}`, args);

      // معالجة الأحداث المختلفة
      switch (eventName) {
        case 'instruments/list':
        case 'balance/list':
        case 'chart_notification/get':
        case 'orders/opened/list':
        case 'orders/closed/list':
          this.emit(eventName, args[0]);
          break;
        case 'quotes/stream':
          this.emit('priceUpdate', args[0]);
          break;
        case 'chart_notification':
          this.emit('candleUpdate', args[0]);
          break;
        default:
          this.emit('socketEvent', { event: eventName, data: args });
      }
    } catch (error) {
      this.logger.error('خطأ في معالجة حدث Socket.IO:', error);
    }
  }

  /**
   * معالجة الرسائل الواردة
   */
  handleMessage(data) {
    try {
      let message;
      if (typeof data === 'string') {
        message = JSON.parse(data);
      } else {
        message = data;
      }

      // معالجة أنواع الرسائل المختلفة
      if (message.event) {
        this.handleEvent(message);
      } else if (message.requestId && this.pendingRequests.has(message.requestId)) {
        this.handleResponse(message);
      } else {
        this.emit('message', message);
      }
    } catch (error) {
      this.logger.error('خطأ في معالجة الرسالة:', error);
    }
  }

  /**
   * معالجة الأحداث
   */
  handleEvent(message) {
    switch (message.event) {
      case 'authorization':
        this.handleAuthorization(message);
        break;
      case 'quotes/stream':
        this.emit('priceUpdate', message.data);
        break;
      case 'chart_notification':
        this.emit('candleUpdate', message.data);
        break;
      case 'orders/opened':
        this.emit('orderOpened', message.data);
        break;
      case 'orders/closed':
        this.emit('orderClosed', message.data);
        break;
      default:
        this.emit('event', message);
    }
  }

  /**
   * معالجة الاستجابات
   */
  handleResponse(message) {
    const request = this.pendingRequests.get(message.requestId);
    if (request) {
      this.pendingRequests.delete(message.requestId);
      
      if (message.error) {
        request.reject(new Error(message.error));
      } else {
        request.resolve(message.data);
      }
    }
  }

  /**
   * معالجة التفويض
   */
  handleAuthorization(message) {
    if (message.data && message.data.session) {
      this.sessionId = message.data.session;
      this.logger.info('تم التفويض بنجاح');
      this.emit('authorized', message.data);
    }
  }

  /**
   * إرسال رسالة
   */
  send(event, data = {}) {
    if (!this.isConnected) {
      throw new Error('غير متصل بالمنصة');
    }

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('انتهت مهلة الاستجابة'));
      }, this.options.timeout);

      // إرسال الحدث والاستماع للاستجابة
      this.socket.emit(event, data, (response) => {
        clearTimeout(timeout);
        if (response && response.error) {
          reject(new Error(response.error));
        } else {
          resolve(response);
        }
      });
    });
  }

  /**
   * إرسال رسالة بدون انتظار استجابة
   */
  emit(event, data = {}) {
    if (!this.isConnected) {
      throw new Error('غير متصل بالمنصة');
    }

    this.socket.emit(event, data);
  }

  /**
   * بدء نبضات القلب
   */
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected && this.socket) {
        this.socket.emit('ping');
      }
    }, 30000); // كل 30 ثانية
  }

  /**
   * إيقاف نبضات القلب
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * معالجة إعادة الاتصال
   */
  async handleReconnection() {
    if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
      this.logger.error('تم الوصول للحد الأقصى من محاولات إعادة الاتصال');
      this.emit('maxReconnectAttemptsReached');
      return;
    }

    this.reconnectAttempts++;
    this.logger.info(`محاولة إعادة الاتصال ${this.reconnectAttempts}/${this.options.maxReconnectAttempts}`);

    setTimeout(async () => {
      try {
        await this.connect();
      } catch (error) {
        this.logger.error('فشل في إعادة الاتصال:', error);
      }
    }, this.options.reconnectInterval);
  }

  /**
   * قطع الاتصال
   */
  disconnect() {
    this.logger.info('قطع الاتصال مع منصة Quotex...');

    this.stopHeartbeat();

    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }

    this.isConnected = false;
    this.sessionId = null;
    this.pendingRequests.clear();
  }

  /**
   * التحقق من حالة الاتصال
   */
  isConnectionActive() {
    return this.isConnected && this.socket && this.socket.connected;
  }

  /**
   * الحصول على معلومات الاتصال
   */
  getConnectionInfo() {
    return {
      isConnected: this.isConnected,
      sessionId: this.sessionId,
      reconnectAttempts: this.reconnectAttempts,
      pendingRequests: this.pendingRequests.size
    };
  }
}

module.exports = QuotexConnector;
