/**
 * كلاس الاتصال الأساسي مع منصة Quotex باستخدام Puppeteer
 * Core Puppeteer Connector for Quotex Platform
 */

const EventEmitter = require('events');
const puppeteer = require('puppeteer-extra');
const StealthPlugin = require('puppeteer-extra-plugin-stealth');
const config = require('../config/config');
const Logger = require('../utils/Logger');

// استخدام plugin التخفي لتجنب الكشف
puppeteer.use(StealthPlugin());

class QuotexConnector extends EventEmitter {
  constructor(options = {}) {
    super();

    this.options = {
      ...config.quotex,
      headless: options.headless !== false,
      userDataDir: options.userDataDir || './user_data',
      ...options
    };

    this.browser = null;
    this.page = null;
    this.isConnected = false;
    this.reconnectAttempts = 0;
    this.sessionId = null;
    this.wsConnection = null;
    this.requestId = 1;
    this.pendingRequests = new Map();

    this.logger = new Logger('QuotexConnector');

    // ربط الأحداث
    this.setupEventHandlers();
  }

  /**
   * إعداد معالجات الأحداث
   */
  setupEventHandlers() {
    this.on('connected', () => {
      this.logger.info('تم الاتصال بنجاح مع منصة Quotex');
      this.isConnected = true;
      this.reconnectAttempts = 0;
      this.startHeartbeat();
    });

    this.on('disconnected', () => {
      this.logger.warn('تم قطع الاتصال مع منصة Quotex');
      this.isConnected = false;
      this.stopHeartbeat();
      this.handleReconnection();
    });

    this.on('error', (error) => {
      this.logger.error('خطأ في الاتصال:', error);
    });
  }

  /**
   * الاتصال بمنصة Quotex باستخدام Puppeteer
   */
  async connect() {
    try {
      this.logger.info('محاولة الاتصال بمنصة Quotex باستخدام Puppeteer...');

      // إطلاق المتصفح
      this.browser = await puppeteer.launch({
        headless: this.options.headless,
        defaultViewport: null,
        userDataDir: this.options.userDataDir,
        args: [
          '--start-maximized',
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      // إنشاء صفحة جديدة
      this.page = await this.browser.newPage();

      // تعيين User Agent
      await this.page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');

      // تفعيل اعتراض الطلبات لمراقبة WebSocket
      await this.page.setRequestInterception(true);

      // مراقبة طلبات الشبكة
      this.page.on('request', (request) => {
        const url = request.url();
        if (url.includes('socket.io') || url.includes('ws://') || url.includes('wss://')) {
          this.logger.debug('WebSocket request detected:', url);
        }
        request.continue();
      });

      // الانتقال إلى منصة التداول
      this.logger.info('الانتقال إلى منصة Quotex...');
      await this.page.goto('https://qxbroker.com/ar/demo-trade', {
        waitUntil: 'networkidle2',
        timeout: this.options.timeout
      });

      // انتظار تحميل الصفحة
      await this.page.waitForTimeout(5000);

      // البحث عن WebSocket connection في الصفحة
      await this.setupWebSocketConnection();

      this.isConnected = true;
      this.emit('connected');

      this.logger.info('تم الاتصال بنجاح مع منصة Quotex');
      return true;

    } catch (error) {
      this.logger.error('فشل في الاتصال:', error);
      throw error;
    }
  }

  /**
   * إعداد اتصال WebSocket داخل الصفحة
   */
  async setupWebSocketConnection() {
    try {
      // حقن كود JavaScript في الصفحة للوصول إلى Socket.IO
      await this.page.evaluateOnNewDocument(() => {
        // تخزين مرجع Socket.IO عندما يتم إنشاؤه
        window.quotexSocketReady = false;
        window.quotexSocket = null;

        // مراقبة إنشاء Socket.IO
        const originalSocketIO = window.io;
        if (originalSocketIO) {
          window.io = function(...args) {
            const socket = originalSocketIO.apply(this, args);
            window.quotexSocket = socket;
            window.quotexSocketReady = true;
            return socket;
          };
        }
      });

      // انتظار تحميل Socket.IO
      await this.page.waitForFunction(() => {
        return window.quotexSocketReady === true && window.quotexSocket !== null;
      }, { timeout: 30000 });

      this.logger.info('تم العثور على اتصال Socket.IO في الصفحة');

      // إعداد معالجات الأحداث
      await this.setupSocketEventHandlers();

    } catch (error) {
      this.logger.warn('لم يتم العثور على Socket.IO، سيتم استخدام طرق بديلة');
      // يمكن إضافة طرق بديلة هنا
    }
  }

  /**
   * إعداد معالجات أحداث Socket.IO
   */
  async setupSocketEventHandlers() {
    await this.page.evaluate(() => {
      if (window.quotexSocket) {
        // معالج الأحداث العام
        window.quotexSocket.onAny((eventName, ...args) => {
          window.quotexEvents = window.quotexEvents || [];
          window.quotexEvents.push({
            event: eventName,
            data: args,
            timestamp: Date.now()
          });

          // الاحتفاظ بآخر 1000 حدث فقط
          if (window.quotexEvents.length > 1000) {
            window.quotexEvents = window.quotexEvents.slice(-1000);
          }
        });
      }
    });
  }

  /**
   * انتظار تأسيس الاتصال
   */
  waitForConnection() {
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error('انتهت مهلة الاتصال'));
      }, this.options.timeout);

      this.once('connected', () => {
        clearTimeout(timeout);
        resolve();
      });

      this.once('error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });
    });
  }

  /**
   * معالجة أحداث Socket.IO
   */
  handleSocketEvent(eventName, args) {
    try {
      this.logger.debug(`حدث Socket.IO: ${eventName}`, args);

      // معالجة الأحداث المختلفة
      switch (eventName) {
        case 'instruments/list':
        case 'balance/list':
        case 'chart_notification/get':
        case 'orders/opened/list':
        case 'orders/closed/list':
          this.emit(eventName, args[0]);
          break;
        case 'quotes/stream':
          this.emit('priceUpdate', args[0]);
          break;
        case 'chart_notification':
          this.emit('candleUpdate', args[0]);
          break;
        default:
          this.emit('socketEvent', { event: eventName, data: args });
      }
    } catch (error) {
      this.logger.error('خطأ في معالجة حدث Socket.IO:', error);
    }
  }

  /**
   * معالجة الرسائل الواردة
   */
  handleMessage(data) {
    try {
      let message;
      if (typeof data === 'string') {
        message = JSON.parse(data);
      } else {
        message = data;
      }

      // معالجة أنواع الرسائل المختلفة
      if (message.event) {
        this.handleEvent(message);
      } else if (message.requestId && this.pendingRequests.has(message.requestId)) {
        this.handleResponse(message);
      } else {
        this.emit('message', message);
      }
    } catch (error) {
      this.logger.error('خطأ في معالجة الرسالة:', error);
    }
  }

  /**
   * معالجة الأحداث
   */
  handleEvent(message) {
    switch (message.event) {
      case 'authorization':
        this.handleAuthorization(message);
        break;
      case 'quotes/stream':
        this.emit('priceUpdate', message.data);
        break;
      case 'chart_notification':
        this.emit('candleUpdate', message.data);
        break;
      case 'orders/opened':
        this.emit('orderOpened', message.data);
        break;
      case 'orders/closed':
        this.emit('orderClosed', message.data);
        break;
      default:
        this.emit('event', message);
    }
  }

  /**
   * معالجة الاستجابات
   */
  handleResponse(message) {
    const request = this.pendingRequests.get(message.requestId);
    if (request) {
      this.pendingRequests.delete(message.requestId);
      
      if (message.error) {
        request.reject(new Error(message.error));
      } else {
        request.resolve(message.data);
      }
    }
  }

  /**
   * معالجة التفويض
   */
  handleAuthorization(message) {
    if (message.data && message.data.session) {
      this.sessionId = message.data.session;
      this.logger.info('تم التفويض بنجاح');
      this.emit('authorized', message.data);
    }
  }

  /**
   * إرسال أمر Socket.IO عبر الصفحة
   */
  async send(event, data = {}) {
    if (!this.isConnected || !this.page) {
      throw new Error('غير متصل بالمنصة');
    }

    try {
      const result = await this.page.evaluate(async (eventName, eventData) => {
        return new Promise((resolve, reject) => {
          if (!window.quotexSocket) {
            reject(new Error('Socket.IO غير متاح'));
            return;
          }

          const timeout = setTimeout(() => {
            reject(new Error('انتهت مهلة الاستجابة'));
          }, 30000);

          // إرسال الحدث
          window.quotexSocket.emit(eventName, eventData, (response) => {
            clearTimeout(timeout);
            resolve(response);
          });
        });
      }, event, data);

      return result;
    } catch (error) {
      this.logger.error(`خطأ في إرسال الحدث ${event}:`, error);
      throw error;
    }
  }

  /**
   * إرسال أمر بدون انتظار استجابة
   */
  async emit(event, data = {}) {
    if (!this.isConnected || !this.page) {
      throw new Error('غير متصل بالمنصة');
    }

    try {
      await this.page.evaluate((eventName, eventData) => {
        if (window.quotexSocket) {
          window.quotexSocket.emit(eventName, eventData);
        }
      }, event, data);
    } catch (error) {
      this.logger.error(`خطأ في إرسال الحدث ${event}:`, error);
      throw error;
    }
  }

  /**
   * جلب الأحداث الجديدة من الصفحة
   */
  async getNewEvents() {
    if (!this.page) return [];

    try {
      const events = await this.page.evaluate(() => {
        const events = window.quotexEvents || [];
        window.quotexEvents = []; // مسح الأحداث بعد جلبها
        return events;
      });

      return events;
    } catch (error) {
      this.logger.error('خطأ في جلب الأحداث:', error);
      return [];
    }
  }

  /**
   * بدء نبضات القلب
   */
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected && this.socket) {
        this.socket.emit('ping');
      }
    }, 30000); // كل 30 ثانية
  }

  /**
   * إيقاف نبضات القلب
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
  }

  /**
   * معالجة إعادة الاتصال
   */
  async handleReconnection() {
    if (this.reconnectAttempts >= this.options.maxReconnectAttempts) {
      this.logger.error('تم الوصول للحد الأقصى من محاولات إعادة الاتصال');
      this.emit('maxReconnectAttemptsReached');
      return;
    }

    this.reconnectAttempts++;
    this.logger.info(`محاولة إعادة الاتصال ${this.reconnectAttempts}/${this.options.maxReconnectAttempts}`);

    setTimeout(async () => {
      try {
        await this.connect();
      } catch (error) {
        this.logger.error('فشل في إعادة الاتصال:', error);
      }
    }, this.options.reconnectInterval);
  }

  /**
   * قطع الاتصال
   */
  async disconnect() {
    this.logger.info('قطع الاتصال مع منصة Quotex...');

    this.isConnected = false;

    try {
      if (this.page) {
        await this.page.close();
        this.page = null;
      }

      if (this.browser) {
        await this.browser.close();
        this.browser = null;
      }
    } catch (error) {
      this.logger.error('خطأ في قطع الاتصال:', error);
    }

    this.sessionId = null;
    this.pendingRequests.clear();
    this.emit('disconnected');
  }

  /**
   * التحقق من حالة الاتصال
   */
  isConnectionActive() {
    return this.isConnected && this.browser && this.page && !this.page.isClosed();
  }

  /**
   * الحصول على معلومات الاتصال
   */
  getConnectionInfo() {
    return {
      isConnected: this.isConnected,
      sessionId: this.sessionId,
      reconnectAttempts: this.reconnectAttempts,
      pendingRequests: this.pendingRequests.size,
      browserConnected: !!this.browser,
      pageActive: this.page && !this.page.isClosed()
    };
  }
}

module.exports = QuotexConnector;
