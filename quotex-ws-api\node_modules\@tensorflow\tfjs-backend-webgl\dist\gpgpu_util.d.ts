/**
 * @license
 * Copyright 2017 Google LLC. All Rights Reserved.
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * =============================================================================
 */
/// <amd-module name="@tensorflow/tfjs-backend-webgl/dist/gpgpu_util" />
import { PixelData, TypedArray } from '@tensorflow/tfjs-core';
import { Texture, TextureConfig } from './tex_util';
export declare function createVertexShader(gl: WebGLRenderingContext): WebGLShader;
export declare function createVertexBuffer(gl: WebGLRenderingContext): WebGLBuffer;
export declare function createIndexBuffer(gl: WebGLRenderingContext): WebGLBuffer;
export declare function getInternalFormatForFloat32MatrixTexture(textureConfig: TextureConfig): number;
export declare function createFloat32MatrixTexture(gl: WebGLRenderingContext, rows: number, columns: number, textureConfig: TextureConfig): Texture;
export declare function getInternalFormatForFloat16MatrixTexture(textureConfig: TextureConfig): number;
export declare function createFloat16MatrixTexture(gl: WebGLRenderingContext, rows: number, columns: number, textureConfig: TextureConfig): Texture;
export declare function getInternalFormatForUnsignedBytesMatrixTexture(textureConfig: TextureConfig): number;
export declare function createUnsignedBytesMatrixTexture(gl: WebGLRenderingContext, rows: number, columns: number, textureConfig: TextureConfig): Texture;
export declare function getInternalFormatForPackedMatrixTexture(textureConfig: TextureConfig): number;
export declare function createPackedMatrixTexture(gl: WebGLRenderingContext, rows: number, columns: number, textureConfig: TextureConfig): Texture;
export declare function getInternalFormatForFloat16PackedMatrixTexture(textureConfig: TextureConfig): number;
export declare function createFloat16PackedMatrixTexture(gl: WebGLRenderingContext, rows: number, columns: number, textureConfig: TextureConfig): Texture;
export declare function bindVertexProgramAttributeStreams(gl: WebGLRenderingContext, program: WebGLProgram, vertexBuffer: WebGLBuffer): boolean;
export declare function uploadDenseMatrixToTexture(gl: WebGLRenderingContext, texture: WebGLTexture, width: number, height: number, data: TypedArray, textureConfig: TextureConfig): void;
export declare function uploadPixelDataToTexture(gl: WebGLRenderingContext, texture: WebGLTexture, pixels: PixelData | ImageData | HTMLImageElement | HTMLCanvasElement | HTMLVideoElement | ImageBitmap): void;
export declare function createBufferFromOutputTexture(gl2: WebGL2RenderingContext, rows: number, columns: number, textureConfig: TextureConfig): WebGLBuffer;
export declare function downloadFloat32MatrixFromBuffer(gl: WebGLRenderingContext, buffer: WebGLBuffer, size: number): Float32Array;
export declare function downloadByteEncodedFloatMatrixFromOutputTexture(gl: WebGLRenderingContext, rows: number, columns: number, textureConfig: TextureConfig): Float32Array;
export declare function downloadPackedMatrixFromBuffer(gl: WebGLRenderingContext, buffer: WebGLBuffer, batch: number, rows: number, cols: number, physicalRows: number, physicalCols: number, textureConfig: TextureConfig): Float32Array;
export declare function downloadMatrixFromPackedOutputTexture(gl: WebGLRenderingContext, physicalRows: number, physicalCols: number): Float32Array;
