<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{3B59A6A2-4B8C-477E-963B-E5B048C1899F}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>webgl</RootNamespace>
    <IgnoreWarnCompileDuplicatedFilename>true</IgnoreWarnCompileDuplicatedFilename>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props"/>
  <PropertyGroup Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
  </PropertyGroup>
  <PropertyGroup Label="Locals">
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props"/>
  <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.props"/>
  <ImportGroup Label="ExtensionSettings"/>
  <ImportGroup Label="PropertySheets">
    <Import Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props"/>
  </ImportGroup>
  <PropertyGroup Label="UserMacros"/>
  <PropertyGroup>
    <ExecutablePath>$(ExecutablePath);$(MSBuildProjectDirectory)\..\bin\;$(MSBuildProjectDirectory)\..\bin\</ExecutablePath>
    <IgnoreImportLibrary>true</IgnoreImportLibrary>
    <OutDir>$(SolutionDir)$(Configuration)\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Configuration)\obj\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(OutDir)obj\$(ProjectName)\</IntDir>
    <LinkIncremental>true</LinkIncremental>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.node</TargetExt>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.node</TargetExt>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.node</TargetExt>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.node</TargetExt>
    <TargetName>$(ProjectName)</TargetName>
    <TargetPath>$(OutDir)\$(ProjectName).node</TargetPath>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\deps\v8\include;..\..\nan;C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl\deps\include;..\angle\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>/Zc:__cplusplus -std:c++20 /Zm2000 %(AdditionalOptions)</AdditionalOptions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4355;4800;4251;4275;4244;4267;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>false</ExceptionHandling>
      <MinimalRebuild>false</MinimalRebuild>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <OmitFramePointers>false</OmitFramePointers>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=webgl;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;NOMINMAX;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;VERSION=1.0.0;WIN32_LEAN_AND_MEAN;VC_EXTRALEAN;BUILDING_NODE_EXTENSION;HOST_BINARY=&quot;node.exe&quot;;DEBUG;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDebug</RuntimeLibrary>
      <StringPooling>true</StringPooling>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>false</TreatWarningAsError>
      <WarningLevel>Level3</WarningLevel>
      <WholeProgramOptimization>true</WholeProgramOptimization>
    </ClCompile>
    <Lib>
      <AdditionalOptions>/LTCG:INCREMENTAL %(AdditionalOptions)</AdditionalOptions>
    </Lib>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;DelayImp.lib;&quot;C:\\Users\\<USER>\\AppData\\Local\\node-gyp\\Cache\\22.16.0\\x64\\node.lib&quot;;libEGL.lib;libGLESv2.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl\deps\win32;C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl\deps\windows\lib\x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>/LTCG:INCREMENTAL /ignore:4199 %(AdditionalOptions)</AdditionalOptions>
      <DelayLoadDLLs>node.exe;%(DelayLoadDLLs)</DelayLoadDLLs>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <OptimizeReferences>true</OptimizeReferences>
      <OutputFile>$(OutDir)$(ProjectName).node</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetExt>.node</TargetExt>
      <TargetMachine>MachineX64</TargetMachine>
    </Link>
    <ResourceCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\deps\v8\include;..\..\nan;C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl\deps\include;..\angle\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=webgl;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;NOMINMAX;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;VERSION=1.0.0;WIN32_LEAN_AND_MEAN;VC_EXTRALEAN;BUILDING_NODE_EXTENSION;HOST_BINARY=&quot;node.exe&quot;;DEBUG;_DEBUG;%(PreprocessorDefinitions);%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\deps\v8\include;..\..\nan;C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl\deps\include;..\angle\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>/Zc:__cplusplus -std:c++20 /Zm2000 /MP %(AdditionalOptions)</AdditionalOptions>
      <BufferSecurityCheck>true</BufferSecurityCheck>
      <DebugInformationFormat>OldStyle</DebugInformationFormat>
      <DisableSpecificWarnings>4351;4355;4800;4251;4275;4244;4267;%(DisableSpecificWarnings)</DisableSpecificWarnings>
      <ExceptionHandling>false</ExceptionHandling>
      <FavorSizeOrSpeed>Speed</FavorSizeOrSpeed>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <MultiProcessorCompilation>true</MultiProcessorCompilation>
      <OmitFramePointers>true</OmitFramePointers>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=webgl;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;NOMINMAX;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;VERSION=1.0.0;WIN32_LEAN_AND_MEAN;VC_EXTRALEAN;BUILDING_NODE_EXTENSION;HOST_BINARY=&quot;node.exe&quot;;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreaded</RuntimeLibrary>
      <RuntimeTypeInfo>false</RuntimeTypeInfo>
      <StringPooling>true</StringPooling>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TreatWarningAsError>false</TreatWarningAsError>
      <WarningLevel>Level3</WarningLevel>
      <WholeProgramOptimization>false</WholeProgramOptimization>
    </ClCompile>
    <Lib>
      <AdditionalOptions>/LTCG:INCREMENTAL %(AdditionalOptions)</AdditionalOptions>
    </Lib>
    <Link>
      <AdditionalDependencies>kernel32.lib;user32.lib;gdi32.lib;winspool.lib;comdlg32.lib;advapi32.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;odbc32.lib;DelayImp.lib;&quot;C:\\Users\\<USER>\\AppData\\Local\\node-gyp\\Cache\\22.16.0\\x64\\node.lib&quot;;libEGL.lib;libGLESv2.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl\deps\win32;C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl\deps\windows\lib\x64;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>/LTCG:INCREMENTAL /ignore:4199 /LTCG:OFF %(AdditionalOptions)</AdditionalOptions>
      <DelayLoadDLLs>node.exe;%(DelayLoadDLLs)</DelayLoadDLLs>
      <EnableCOMDATFolding>false</EnableCOMDATFolding>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <LinkTimeCodeGeneration>Default</LinkTimeCodeGeneration>
      <OptimizeReferences>false</OptimizeReferences>
      <OutputFile>$(OutDir)$(ProjectName).node</OutputFile>
      <SuppressStartupBanner>true</SuppressStartupBanner>
      <TargetExt>.node</TargetExt>
      <TargetMachine>MachineX64</TargetMachine>
    </Link>
    <ResourceCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\include\node;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\src;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\deps\openssl\config;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\deps\openssl\openssl\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\deps\uv\include;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\deps\zlib;C:\Users\<USER>\AppData\Local\node-gyp\Cache\22.16.0\deps\v8\include;..\..\nan;C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl\deps\include;..\angle\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NODE_GYP_MODULE_NAME=webgl;USING_UV_SHARED=1;USING_V8_SHARED=1;V8_DEPRECATION_WARNINGS=1;_GLIBCXX_USE_CXX11_ABI=1;WIN32;_CRT_SECURE_NO_DEPRECATE;_CRT_NONSTDC_NO_DEPRECATE;_HAS_EXCEPTIONS=0;NOMINMAX;OPENSSL_NO_PINSHARED;OPENSSL_THREADS;VERSION=1.0.0;WIN32_LEAN_AND_MEAN;VC_EXTRALEAN;BUILDING_NODE_EXTENSION;HOST_BINARY=&quot;node.exe&quot;;%(PreprocessorDefinitions);%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <None Include="..\binding.gyp"/>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="..\src\native\bindings.cc">
      <ObjectFileName>$(IntDir)\src\native\bindings.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\src\native\webgl.cc">
      <ObjectFileName>$(IntDir)\src\native\webgl.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="..\src\native\procs.cc">
      <ObjectFileName>$(IntDir)\src\native\procs.obj</ObjectFileName>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\node-gyp\src\win_delay_load_hook.cc"/>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl\deps\windows\dll\x64\libEGL.dll">
      <FileType>Document</FileType>
      <Command>call mkdir &quot;$(SolutionDir)$(Configuration)&quot; 2&gt;nul &amp; set ERRORLEVEL=0 &amp; copy /Y &quot;C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl\deps\windows\dll\x64\libEGL.dll&quot; &quot;$(SolutionDir)$(Configuration)\libEGL.dll&quot;&#xD;&#xA;if %errorlevel% neq 0 exit /b %errorlevel%</Command>
      <Message>Copying C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl/deps/windows/dll/x64/libEGL.dll to $(SolutionDir)$(ConfigurationName)</Message>
      <Outputs>$(SolutionDir)$(ConfigurationName)\libEGL.dll</Outputs>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl\deps\windows\dll\x64\libGLESv2.dll">
      <FileType>Document</FileType>
      <Command>call mkdir &quot;$(SolutionDir)$(Configuration)&quot; 2&gt;nul &amp; set ERRORLEVEL=0 &amp; copy /Y &quot;C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl\deps\windows\dll\x64\libGLESv2.dll&quot; &quot;$(SolutionDir)$(Configuration)\libGLESv2.dll&quot;&#xD;&#xA;if %errorlevel% neq 0 exit /b %errorlevel%</Command>
      <Message>Copying C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl/deps/windows/dll/x64/libGLESv2.dll to $(SolutionDir)$(ConfigurationName)</Message>
      <Outputs>$(SolutionDir)$(ConfigurationName)\libGLESv2.dll</Outputs>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl\deps\windows\dll\x64\d3dcompiler_47.dll">
      <FileType>Document</FileType>
      <Command>call mkdir &quot;$(SolutionDir)$(Configuration)&quot; 2&gt;nul &amp; set ERRORLEVEL=0 &amp; copy /Y &quot;C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl\deps\windows\dll\x64\d3dcompiler_47.dll&quot; &quot;$(SolutionDir)$(Configuration)\d3dcompiler_47.dll&quot;&#xD;&#xA;if %errorlevel% neq 0 exit /b %errorlevel%</Command>
      <Message>Copying C:\Users\<USER>\Downloads\quotex-ws-api (1)\node_modules\gl/deps/windows/dll/x64/d3dcompiler_47.dll to $(SolutionDir)$(ConfigurationName)</Message>
      <Outputs>$(SolutionDir)$(ConfigurationName)\d3dcompiler_47.dll</Outputs>
    </CustomBuild>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets"/>
  <Import Project="$(VCTargetsPath)\BuildCustomizations\masm.targets"/>
  <ImportGroup Label="ExtensionTargets"/>
</Project>
