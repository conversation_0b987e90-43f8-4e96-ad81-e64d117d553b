/**
 * طبقة التحليل السلوكي - Behavioral Analysis Layer
 * مسؤولة عن تحليل السلوك السعري وأنماط الشموع
 */

const EventEmitter = require('events');
const config = require('../config/config');
const Logger = require('../utils/Logger');

class BehavioralAnalyzer extends EventEmitter {
  constructor() {
    super();
    
    this.logger = new Logger('BehavioralAnalyzer');
    
    // تخزين التحليلات
    this.behaviorAnalysis = new Map(); // pair -> analysis object
    this.patterns = new Map(); // pair -> detected patterns
  }

  /**
   * تحليل السلوك السعري لزوج معين
   */
  analyzeBehavior(pair, candles, indicators) {
    try {
      if (!candles || candles.length < 10) {
        throw new Error(`بيانات غير كافية للتحليل السلوكي للزوج ${pair}`);
      }

      const analysis = {
        timestamp: Date.now(),
        pair,
        candlePatterns: this.analyzeCandlePatterns(candles),
        priceAction: this.analyzePriceAction(candles),
        momentum: this.analyzeMomentum(candles),
        volumeAnalysis: this.analyzeVolume(candles),
        marketStructure: this.analyzeMarketStructure(candles),
        behaviorSignals: this.generateBehaviorSignals(candles, indicators)
      };

      // حفظ النتائج
      this.behaviorAnalysis.set(pair, analysis);
      
      // إرسال إشعار بالتحديث
      this.emit('behaviorAnalyzed', {
        pair,
        analysis
      });

      this.logger.debug(`تم تحليل السلوك للزوج ${pair}`);
      
      return analysis;
      
    } catch (error) {
      this.logger.error(`خطأ في التحليل السلوكي للزوج ${pair}:`, error);
      throw error;
    }
  }

  /**
   * تحليل أنماط الشموع
   */
  analyzeCandlePatterns(candles) {
    const recentCandles = candles.slice(-5); // آخر 5 شموع
    const patterns = [];

    // فحص الأنماط المختلفة
    patterns.push(...this.detectSingleCandlePatterns(recentCandles));
    patterns.push(...this.detectMultiCandlePatterns(recentCandles));
    
    return {
      detected: patterns,
      strength: this.calculatePatternStrength(patterns),
      reliability: this.calculatePatternReliability(patterns)
    };
  }

  /**
   * كشف أنماط الشمعة الواحدة
   */
  detectSingleCandlePatterns(candles) {
    const patterns = [];
    const lastCandle = candles[candles.length - 1];
    
    if (!lastCandle) return patterns;

    const body = Math.abs(lastCandle.close - lastCandle.open);
    const range = lastCandle.high - lastCandle.low;
    const upperShadow = lastCandle.high - Math.max(lastCandle.open, lastCandle.close);
    const lowerShadow = Math.min(lastCandle.open, lastCandle.close) - lastCandle.low;

    // Doji
    if (body / range < 0.1) {
      patterns.push({
        name: 'Doji',
        type: 'reversal',
        strength: 'medium',
        direction: 'neutral'
      });
    }

    // Hammer / Hanging Man
    if (lowerShadow > body * 2 && upperShadow < body * 0.5) {
      patterns.push({
        name: lastCandle.close > lastCandle.open ? 'Hammer' : 'Hanging Man',
        type: 'reversal',
        strength: 'high',
        direction: lastCandle.close > lastCandle.open ? 'bullish' : 'bearish'
      });
    }

    // Shooting Star / Inverted Hammer
    if (upperShadow > body * 2 && lowerShadow < body * 0.5) {
      patterns.push({
        name: lastCandle.close > lastCandle.open ? 'Inverted Hammer' : 'Shooting Star',
        type: 'reversal',
        strength: 'high',
        direction: lastCandle.close > lastCandle.open ? 'bullish' : 'bearish'
      });
    }

    // Marubozu
    if (body / range > 0.95) {
      patterns.push({
        name: 'Marubozu',
        type: 'continuation',
        strength: 'high',
        direction: lastCandle.close > lastCandle.open ? 'bullish' : 'bearish'
      });
    }

    // Pin Bar
    if ((upperShadow > body * 3 || lowerShadow > body * 3) && body / range < 0.3) {
      patterns.push({
        name: 'Pin Bar',
        type: 'reversal',
        strength: 'high',
        direction: upperShadow > lowerShadow ? 'bearish' : 'bullish'
      });
    }

    return patterns;
  }

  /**
   * كشف أنماط الشموع المتعددة
   */
  detectMultiCandlePatterns(candles) {
    const patterns = [];
    
    if (candles.length < 2) return patterns;

    // Engulfing Pattern
    const engulfing = this.detectEngulfingPattern(candles);
    if (engulfing) patterns.push(engulfing);

    // Harami Pattern
    const harami = this.detectHaramiPattern(candles);
    if (harami) patterns.push(harami);

    // Three White Soldiers / Three Black Crows
    if (candles.length >= 3) {
      const threeSoldiers = this.detectThreeSoldiersPattern(candles);
      if (threeSoldiers) patterns.push(threeSoldiers);
    }

    return patterns;
  }

  /**
   * كشف نمط الابتلاع
   */
  detectEngulfingPattern(candles) {
    if (candles.length < 2) return null;

    const prev = candles[candles.length - 2];
    const current = candles[candles.length - 1];

    const prevBody = Math.abs(prev.close - prev.open);
    const currentBody = Math.abs(current.close - current.open);

    // شروط الابتلاع
    if (currentBody > prevBody * 1.5 &&
        current.high > prev.high &&
        current.low < prev.low &&
        (prev.close > prev.open) !== (current.close > current.open)) {
      
      return {
        name: 'Engulfing',
        type: 'reversal',
        strength: 'high',
        direction: current.close > current.open ? 'bullish' : 'bearish'
      };
    }

    return null;
  }

  /**
   * كشف نمط الحرامي
   */
  detectHaramiPattern(candles) {
    if (candles.length < 2) return null;

    const prev = candles[candles.length - 2];
    const current = candles[candles.length - 1];

    const prevBody = Math.abs(prev.close - prev.open);
    const currentBody = Math.abs(current.close - current.open);

    // شروط الحرامي
    if (currentBody < prevBody * 0.7 &&
        current.high < prev.high &&
        current.low > prev.low &&
        (prev.close > prev.open) !== (current.close > current.open)) {
      
      return {
        name: 'Harami',
        type: 'reversal',
        strength: 'medium',
        direction: current.close > current.open ? 'bullish' : 'bearish'
      };
    }

    return null;
  }

  /**
   * كشف نمط الجنود الثلاثة
   */
  detectThreeSoldiersPattern(candles) {
    if (candles.length < 3) return null;

    const last3 = candles.slice(-3);
    
    // فحص الجنود البيض الثلاثة
    const allBullish = last3.every(c => c.close > c.open);
    const increasingCloses = last3[0].close < last3[1].close && last3[1].close < last3[2].close;
    
    if (allBullish && increasingCloses) {
      return {
        name: 'Three White Soldiers',
        type: 'continuation',
        strength: 'high',
        direction: 'bullish'
      };
    }

    // فحص الغربان السود الثلاثة
    const allBearish = last3.every(c => c.close < c.open);
    const decreasingCloses = last3[0].close > last3[1].close && last3[1].close > last3[2].close;
    
    if (allBearish && decreasingCloses) {
      return {
        name: 'Three Black Crows',
        type: 'continuation',
        strength: 'high',
        direction: 'bearish'
      };
    }

    return null;
  }

  /**
   * تحليل حركة السعر
   */
  analyzePriceAction(candles) {
    const recentCandles = candles.slice(-10);
    
    return {
      trend: this.identifyTrend(recentCandles),
      support: this.findSupportLevel(recentCandles),
      resistance: this.findResistanceLevel(recentCandles),
      breakout: this.detectBreakout(recentCandles),
      consolidation: this.detectConsolidation(recentCandles)
    };
  }

  /**
   * تحديد الاتجاه
   */
  identifyTrend(candles) {
    if (candles.length < 5) return 'sideways';

    const closes = candles.map(c => c.close);
    const highs = candles.map(c => c.high);
    const lows = candles.map(c => c.low);

    // حساب المتوسط المتحرك البسيط
    const sma = closes.reduce((sum, close) => sum + close, 0) / closes.length;
    const currentPrice = closes[closes.length - 1];

    // تحليل القمم والقيعان
    const recentHighs = highs.slice(-5);
    const recentLows = lows.slice(-5);
    
    const higherHighs = recentHighs[4] > recentHighs[0];
    const higherLows = recentLows[4] > recentLows[0];
    const lowerHighs = recentHighs[4] < recentHighs[0];
    const lowerLows = recentLows[4] < recentLows[0];

    if (higherHighs && higherLows && currentPrice > sma) {
      return 'uptrend';
    } else if (lowerHighs && lowerLows && currentPrice < sma) {
      return 'downtrend';
    } else {
      return 'sideways';
    }
  }

  /**
   * العثور على مستوى الدعم
   */
  findSupportLevel(candles) {
    const lows = candles.map(c => c.low);
    const minLow = Math.min(...lows);
    
    // البحث عن مستويات الدعم المتكررة
    const supportLevels = [];
    lows.forEach(low => {
      if (Math.abs(low - minLow) / minLow < 0.005) { // ضمن 0.5%
        supportLevels.push(low);
      }
    });

    return supportLevels.length > 1 ? minLow : null;
  }

  /**
   * العثور على مستوى المقاومة
   */
  findResistanceLevel(candles) {
    const highs = candles.map(c => c.high);
    const maxHigh = Math.max(...highs);
    
    // البحث عن مستويات المقاومة المتكررة
    const resistanceLevels = [];
    highs.forEach(high => {
      if (Math.abs(high - maxHigh) / maxHigh < 0.005) { // ضمن 0.5%
        resistanceLevels.push(high);
      }
    });

    return resistanceLevels.length > 1 ? maxHigh : null;
  }

  /**
   * كشف الاختراق
   */
  detectBreakout(candles) {
    if (candles.length < 5) return null;

    const recent = candles.slice(-5);
    const older = candles.slice(-10, -5);
    
    const recentHigh = Math.max(...recent.map(c => c.high));
    const recentLow = Math.min(...recent.map(c => c.low));
    const olderHigh = Math.max(...older.map(c => c.high));
    const olderLow = Math.min(...older.map(c => c.low));

    if (recentHigh > olderHigh * 1.01) {
      return {
        type: 'upward',
        strength: (recentHigh - olderHigh) / olderHigh * 100
      };
    } else if (recentLow < olderLow * 0.99) {
      return {
        type: 'downward',
        strength: (olderLow - recentLow) / olderLow * 100
      };
    }

    return null;
  }

  /**
   * كشف التماسك
   */
  detectConsolidation(candles) {
    if (candles.length < 5) return false;

    const highs = candles.map(c => c.high);
    const lows = candles.map(c => c.low);
    
    const range = Math.max(...highs) - Math.min(...lows);
    const avgPrice = (Math.max(...highs) + Math.min(...lows)) / 2;
    
    // إذا كان النطاق أقل من 1% من متوسط السعر
    return (range / avgPrice) < 0.01;
  }

  /**
   * تحليل الزخم
   */
  analyzeMomentum(candles) {
    if (candles.length < 3) return null;

    const recent = candles.slice(-3);
    const bodySize = recent.map(c => Math.abs(c.close - c.open));
    const avgBodySize = bodySize.reduce((sum, size) => sum + size, 0) / bodySize.length;
    
    return {
      strength: avgBodySize,
      acceleration: bodySize[2] > bodySize[1] && bodySize[1] > bodySize[0],
      deceleration: bodySize[2] < bodySize[1] && bodySize[1] < bodySize[0],
      consistency: bodySize.every(size => Math.abs(size - avgBodySize) / avgBodySize < 0.3)
    };
  }

  /**
   * تحليل الحجم
   */
  analyzeVolume(candles) {
    const volumes = candles.map(c => c.volume || 0);
    const avgVolume = volumes.reduce((sum, vol) => sum + vol, 0) / volumes.length;
    const currentVolume = volumes[volumes.length - 1];
    
    return {
      current: currentVolume,
      average: avgVolume,
      spike: currentVolume > avgVolume * 1.5,
      trend: this.calculateVolumeTrend(volumes)
    };
  }

  /**
   * حساب اتجاه الحجم
   */
  calculateVolumeTrend(volumes) {
    if (volumes.length < 5) return 'neutral';

    const recent = volumes.slice(-3);
    const older = volumes.slice(-6, -3);
    
    const recentAvg = recent.reduce((sum, vol) => sum + vol, 0) / recent.length;
    const olderAvg = older.reduce((sum, vol) => sum + vol, 0) / older.length;
    
    if (recentAvg > olderAvg * 1.2) {
      return 'increasing';
    } else if (recentAvg < olderAvg * 0.8) {
      return 'decreasing';
    } else {
      return 'stable';
    }
  }

  /**
   * تحليل هيكل السوق
   */
  analyzeMarketStructure(candles) {
    return {
      volatility: this.calculateVolatility(candles),
      efficiency: this.calculateMarketEfficiency(candles),
      noise: this.calculateNoiseLevel(candles)
    };
  }

  /**
   * حساب التقلب
   */
  calculateVolatility(candles) {
    const returns = [];
    for (let i = 1; i < candles.length; i++) {
      const ret = (candles[i].close - candles[i-1].close) / candles[i-1].close;
      returns.push(ret);
    }
    
    const avgReturn = returns.reduce((sum, ret) => sum + ret, 0) / returns.length;
    const variance = returns.reduce((sum, ret) => sum + Math.pow(ret - avgReturn, 2), 0) / returns.length;
    
    return Math.sqrt(variance);
  }

  /**
   * حساب كفاءة السوق
   */
  calculateMarketEfficiency(candles) {
    // حساب مبسط لكفاءة السوق
    const priceChanges = [];
    for (let i = 1; i < candles.length; i++) {
      priceChanges.push(candles[i].close - candles[i-1].close);
    }
    
    const totalChange = Math.abs(candles[candles.length-1].close - candles[0].close);
    const sumAbsChanges = priceChanges.reduce((sum, change) => sum + Math.abs(change), 0);
    
    return totalChange / sumAbsChanges;
  }

  /**
   * حساب مستوى الضوضاء
   */
  calculateNoiseLevel(candles) {
    const ranges = candles.map(c => c.high - c.low);
    const bodies = candles.map(c => Math.abs(c.close - c.open));
    
    const avgRange = ranges.reduce((sum, range) => sum + range, 0) / ranges.length;
    const avgBody = bodies.reduce((sum, body) => sum + body, 0) / bodies.length;
    
    return 1 - (avgBody / avgRange);
  }

  /**
   * توليد إشارات السلوك
   */
  generateBehaviorSignals(candles, indicators) {
    const signals = {
      reversal: 0,
      continuation: 0,
      breakout: 0,
      consolidation: 0
    };

    const analysis = this.behaviorAnalysis.get('temp') || {};
    
    // إشارات الانعكاس
    if (analysis.candlePatterns && analysis.candlePatterns.detected) {
      analysis.candlePatterns.detected.forEach(pattern => {
        if (pattern.type === 'reversal') {
          signals.reversal += pattern.strength === 'high' ? 2 : 1;
        } else if (pattern.type === 'continuation') {
          signals.continuation += pattern.strength === 'high' ? 2 : 1;
        }
      });
    }

    // إشارات الاختراق
    if (analysis.priceAction && analysis.priceAction.breakout) {
      signals.breakout += 2;
    }

    // إشارات التماسك
    if (analysis.priceAction && analysis.priceAction.consolidation) {
      signals.consolidation += 1;
    }

    return signals;
  }

  /**
   * حساب قوة النمط
   */
  calculatePatternStrength(patterns) {
    if (!patterns || patterns.length === 0) return 0;

    const strengthMap = { high: 3, medium: 2, low: 1 };
    const totalStrength = patterns.reduce((sum, pattern) => {
      return sum + (strengthMap[pattern.strength] || 1);
    }, 0);

    return Math.min(totalStrength / patterns.length, 3);
  }

  /**
   * حساب موثوقية النمط
   */
  calculatePatternReliability(patterns) {
    if (!patterns || patterns.length === 0) return 0;

    // حساب مبسط للموثوقية
    const reliablePatterns = patterns.filter(p => p.strength === 'high').length;
    return (reliablePatterns / patterns.length) * 100;
  }

  /**
   * الحصول على التحليل السلوكي لزوج معين
   */
  getBehaviorAnalysis(pair) {
    return this.behaviorAnalysis.get(pair);
  }
}

module.exports = BehavioralAnalyzer;
