/**
 * اختبار الاتصال مع منصة Quotex
 * Connection Test for Quotex Platform
 */

const QuotexConnector = require('./src/core/QuotexConnector');
const DataCollector = require('./src/layers/DataCollector');
const Logger = require('./src/utils/Logger');

class ConnectionTest {
  constructor() {
    this.logger = new Logger('ConnectionTest');
    this.connector = null;
    this.dataCollector = null;
    this.testResults = {
      connection: false,
      pairsList: false,
      historicalData: false,
      liveData: false,
      errors: []
    };
  }

  /**
   * تشغيل جميع الاختبارات
   */
  async runAllTests() {
    this.logger.info('🚀 بدء اختبار الاتصال مع منصة Quotex...');
    
    try {
      // اختبار 1: الاتصال الأساسي
      await this.testBasicConnection();
      
      // اختبار 2: جلب قائمة الأزواج
      await this.testPairsList();
      
      // اختبار 3: جلب البيانات التاريخية
      await this.testHistoricalData();
      
      // اختبار 4: البث المباشر
      await this.testLiveData();
      
      // عرض النتائج
      this.displayResults();
      
    } catch (error) {
      this.logger.error('خطأ في الاختبار:', error);
      this.testResults.errors.push(error.message);
    } finally {
      await this.cleanup();
    }
  }

  /**
   * اختبار الاتصال الأساسي
   */
  async testBasicConnection() {
    this.logger.info('📡 اختبار الاتصال الأساسي...');
    
    try {
      this.connector = new QuotexConnector();
      
      // محاولة الاتصال
      await this.connector.connect();
      
      if (this.connector.isConnectionActive()) {
        this.testResults.connection = true;
        this.logger.info('✅ تم الاتصال بنجاح!');
        
        // عرض معلومات الاتصال
        const connectionInfo = this.connector.getConnectionInfo();
        this.logger.info('معلومات الاتصال:', connectionInfo);
        
      } else {
        throw new Error('فشل في تأسيس الاتصال');
      }
      
    } catch (error) {
      this.logger.error('❌ فشل في الاتصال:', error);
      this.testResults.errors.push(`Connection: ${error.message}`);
      throw error;
    }
  }

  /**
   * اختبار جلب قائمة الأزواج
   */
  async testPairsList() {
    this.logger.info('📋 اختبار جلب قائمة الأزواج...');
    
    try {
      this.dataCollector = new DataCollector(this.connector);
      
      const pairs = await this.dataCollector.fetchAvailablePairs();
      
      if (pairs && pairs.length > 0) {
        this.testResults.pairsList = true;
        this.logger.info(`✅ تم جلب ${pairs.length} زوج بنجاح!`);
        
        // عرض أول 5 أزواج كمثال
        this.logger.info('أمثلة على الأزواج المتاحة:');
        pairs.slice(0, 5).forEach(pair => {
          this.logger.info(`  - ${pair.symbol}: ${pair.name} (${pair.payout})`);
        });
        
      } else {
        throw new Error('لم يتم جلب أي أزواج');
      }
      
    } catch (error) {
      this.logger.error('❌ فشل في جلب قائمة الأزواج:', error);
      this.testResults.errors.push(`Pairs List: ${error.message}`);
    }
  }

  /**
   * اختبار جلب البيانات التاريخية
   */
  async testHistoricalData() {
    this.logger.info('📊 اختبار جلب البيانات التاريخية...');
    
    try {
      const testPair = 'EURUSD_otc';
      
      const candles = await this.dataCollector.fetchPairHistoricalData(testPair);
      
      if (candles && candles.length > 0) {
        this.testResults.historicalData = true;
        this.logger.info(`✅ تم جلب ${candles.length} شمعة للزوج ${testPair}!`);
        
        // عرض آخر شمعة كمثال
        const lastCandle = candles[candles.length - 1];
        this.logger.info('آخر شمعة:', {
          timestamp: new Date(lastCandle.timestamp).toLocaleString(),
          open: lastCandle.open,
          high: lastCandle.high,
          low: lastCandle.low,
          close: lastCandle.close,
          volume: lastCandle.volume
        });
        
      } else {
        throw new Error('لم يتم جلب أي بيانات تاريخية');
      }
      
    } catch (error) {
      this.logger.error('❌ فشل في جلب البيانات التاريخية:', error);
      this.testResults.errors.push(`Historical Data: ${error.message}`);
    }
  }

  /**
   * اختبار البث المباشر
   */
  async testLiveData() {
    this.logger.info('📡 اختبار البث المباشر...');
    
    try {
      const testPair = 'EURUSD_otc';
      let priceUpdateReceived = false;
      
      // الاستماع لتحديثات الأسعار
      this.dataCollector.on('priceUpdated', (data) => {
        if (data.pair === testPair) {
          priceUpdateReceived = true;
          this.logger.info(`💰 تحديث سعر ${data.pair}: ${data.price}`);
        }
      });
      
      // الاشتراك في البث المباشر
      await this.dataCollector.subscribeToPair(testPair);
      
      // انتظار تحديث السعر لمدة 10 ثواني
      await new Promise((resolve) => {
        const timeout = setTimeout(() => {
          resolve();
        }, 10000);
        
        this.dataCollector.on('priceUpdated', (data) => {
          if (data.pair === testPair) {
            clearTimeout(timeout);
            resolve();
          }
        });
      });
      
      if (priceUpdateReceived) {
        this.testResults.liveData = true;
        this.logger.info('✅ تم استقبال البث المباشر بنجاح!');
      } else {
        throw new Error('لم يتم استقبال أي تحديثات للأسعار');
      }
      
    } catch (error) {
      this.logger.error('❌ فشل في البث المباشر:', error);
      this.testResults.errors.push(`Live Data: ${error.message}`);
    }
  }

  /**
   * عرض النتائج
   */
  displayResults() {
    this.logger.info('\n📋 ملخص نتائج الاختبار:');
    this.logger.info('================================');
    
    const tests = [
      { name: 'الاتصال الأساسي', result: this.testResults.connection },
      { name: 'قائمة الأزواج', result: this.testResults.pairsList },
      { name: 'البيانات التاريخية', result: this.testResults.historicalData },
      { name: 'البث المباشر', result: this.testResults.liveData }
    ];
    
    tests.forEach(test => {
      const status = test.result ? '✅ نجح' : '❌ فشل';
      this.logger.info(`${test.name}: ${status}`);
    });
    
    const successCount = tests.filter(test => test.result).length;
    const totalTests = tests.length;
    
    this.logger.info(`\nالنتيجة الإجمالية: ${successCount}/${totalTests} اختبارات نجحت`);
    
    if (this.testResults.errors.length > 0) {
      this.logger.info('\n🚨 الأخطاء:');
      this.testResults.errors.forEach(error => {
        this.logger.error(`  - ${error}`);
      });
    }
    
    if (successCount === totalTests) {
      this.logger.info('\n🎉 جميع الاختبارات نجحت! النظام جاهز للعمل.');
    } else {
      this.logger.warn('\n⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.');
    }
  }

  /**
   * تنظيف الموارد
   */
  async cleanup() {
    this.logger.info('🧹 تنظيف الموارد...');
    
    try {
      if (this.dataCollector) {
        await this.dataCollector.stopCollection();
      }
      
      if (this.connector) {
        this.connector.disconnect();
      }
      
      this.logger.info('✅ تم تنظيف الموارد بنجاح');
      
    } catch (error) {
      this.logger.error('خطأ في تنظيف الموارد:', error);
    }
  }
}

// تشغيل الاختبار
async function main() {
  const test = new ConnectionTest();
  await test.runAllTests();
  
  // إنهاء العملية
  process.exit(0);
}

// التعامل مع الإشارات
process.on('SIGINT', async () => {
  console.log('\n⏹️ تم إيقاف الاختبار بواسطة المستخدم');
  process.exit(0);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// تشغيل الاختبار إذا تم استدعاء الملف مباشرة
if (require.main === module) {
  main().catch(console.error);
}

module.exports = ConnectionTest;
