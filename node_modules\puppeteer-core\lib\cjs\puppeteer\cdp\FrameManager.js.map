{"version": 3, "file": "FrameManager.js", "sourceRoot": "", "sources": ["../../../../src/cdp/FrameManager.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAIH,wDAAsE;AACtE,8CAA2C;AAC3C,+DAAuD;AAEvD,+CAA+E;AAC/E,iDAAyC;AACzC,qDAA6C;AAC7C,yDAAoD;AACpD,uDAAiD;AAEjD,mDAA8C;AAC9C,mDAAoD;AACpD,qEAAoE;AACpE,+DAAuD;AACvD,yCAAoC;AAEpC,mEAA0D;AAC1D,iDAAyC;AAEzC,2DAAgE;AAChE,2DAAmD;AAInD,MAAM,yBAAyB,GAAG,GAAG,CAAC,CAAC,MAAM;AAE7C;;;;GAIG;AACH,MAAa,YAAa,SAAQ,8BAAgC;IAChE,KAAK,CAAU;IACf,eAAe,CAAiB;IAChC,gBAAgB,CAAkB;IAClC,mBAAmB,GAAG,IAAI,GAAG,EAA4B,CAAC;IAC1D,eAAe,GAAG,IAAI,GAAG,EAAU,CAAC;IACpC,OAAO,CAAa;IAEpB,UAAU,GAAG,IAAI,wBAAS,EAAY,CAAC;IAEvC;;;;OAIG;IACH,uBAAuB,GAAG,IAAI,GAAG,EAAU,CAAC;IAE5C,8BAA8B,GAAG,IAAI,OAAO,EAGzC,CAAC;IAEJ,iBAAiB,CAAkB;IAEnC,IAAI,eAAe;QACjB,OAAO,IAAI,CAAC,gBAAgB,CAAC;IAC/B,CAAC;IAED,IAAI,cAAc;QAChB,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED,YACE,MAAkB,EAClB,IAAa,EACb,iBAA0B,EAC1B,eAAgC;QAEhC,KAAK,EAAE,CAAC;QACR,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;QAClB,IAAI,CAAC,eAAe,GAAG,IAAI,kCAAc,CAAC,iBAAiB,EAAE,IAAI,CAAC,CAAC;QACnE,IAAI,CAAC,gBAAgB,GAAG,eAAe,CAAC;QACxC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACvC,MAAM,CAAC,IAAI,CAAC,+BAAe,CAAC,YAAY,EAAE,GAAG,EAAE;YAC7C,IAAI,CAAC,mBAAmB,EAAE,CAAC,KAAK,CAAC,oBAAU,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;IACL,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,mBAAmB;QACvB,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;QACjD,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO;QACT,CAAC;QACD,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,WAAW,EAAE,EAAE,CAAC;YAC5C,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC;QACD,MAAM,OAAO,GAAG,sBAAQ,CAAC,MAAM,CAAO;YACpC,OAAO,EAAE,yBAAyB;YAClC,OAAO,EAAE,uBAAuB;SACjC,CAAC,CAAC;QACH,SAAS,CAAC,IAAI,CAAC,qBAAU,CAAC,wBAAwB,EAAE,GAAG,EAAE;YACvD,OAAO,CAAC,OAAO,EAAE,CAAC;QACpB,CAAC,CAAC,CAAC;QACH,IAAI,CAAC;YACH,MAAM,OAAO,CAAC,YAAY,EAAE,CAAC;QAC/B,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,IAAI,CAAC,wBAAwB,CAAC,SAAS,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAED;;;;OAIG;IACH,KAAK,CAAC,aAAa,CAAC,MAAkB;QACpC,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE/C,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAA,kBAAM,EACJ,IAAI,CAAC,OAAO,YAAY,6BAAa,EACrC,kDAAkD,CACnD,CAAC;QACF,MAAM,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;QAC7C,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,SAAS,CAAC,CAAC;YACnE,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YACnC,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,SAAS,CAAC,CAAC;YACjD,KAAK,CAAC,SAAS,EAAE,CAAC,YAAY,EAAE,CAAC;YACjC,KAAK,CAAC,aAAa,EAAE,CAAC,YAAY,EAAE,CAAC;YACrC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YAChC,KAAK,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACnC,CAAC;QACD,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,CAAC;QACjC,MAAM,CAAC,IAAI,CAAC,+BAAe,CAAC,YAAY,EAAE,GAAG,EAAE;YAC7C,IAAI,CAAC,mBAAmB,EAAE,CAAC,KAAK,CAAC,oBAAU,CAAC,CAAC;QAC/C,CAAC,CAAC,CAAC;QACH,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;QAC9B,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;QAC7C,IAAI,KAAK,EAAE,CAAC;YACV,KAAK,CAAC,IAAI,CAAC,qBAAU,CAAC,wBAAwB,EAAE,SAAS,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,0BAA0B,CAAC,MAAqB;QACpD,MAAM,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC;IAC/C,CAAC;IAEO,mBAAmB,CAAC,OAAmB;QAC7C,OAAO,CAAC,EAAE,CAAC,oBAAoB,EAAE,KAAK,EAAC,KAAK,EAAC,EAAE;YAC7C,MAAM,IAAI,CAAC,iBAAiB,EAAE,YAAY,EAAE,CAAC;YAC7C,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,aAAa,CAAC,CAAC;QACrE,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,qBAAqB,EAAE,KAAK,EAAC,KAAK,EAAC,EAAE;YAC9C,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YACjD,MAAM,IAAI,CAAC,iBAAiB,EAAE,YAAY,EAAE,CAAC;YAC7C,KAAK,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QACvD,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,8BAA8B,EAAE,KAAK,EAAC,KAAK,EAAC,EAAE;YACvD,MAAM,IAAI,CAAC,iBAAiB,EAAE,YAAY,EAAE,CAAC;YAC7C,IAAI,CAAC,+BAA+B,CAAC,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,GAAG,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CACR,oBAAoB,EACpB,KAAK,EAAE,KAAuC,EAAE,EAAE;YAChD,MAAM,IAAI,CAAC,iBAAiB,EAAE,YAAY,EAAE,CAAC;YAC7C,IAAI,CAAC,gBAAgB,CACnB,KAAK,CAAC,OAAO,EACb,KAAK,CAAC,MAAgD,CACvD,CAAC;QACJ,CAAC,CACF,CAAC;QACF,OAAO,CAAC,EAAE,CAAC,0BAA0B,EAAE,KAAK,EAAC,KAAK,EAAC,EAAE;YACnD,MAAM,IAAI,CAAC,iBAAiB,EAAE,YAAY,EAAE,CAAC;YAC7C,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,0BAA0B,EAAE,KAAK,EAAC,KAAK,EAAC,EAAE;YACnD,MAAM,IAAI,CAAC,iBAAiB,EAAE,YAAY,EAAE,CAAC;YAC7C,IAAI,CAAC,sBAAsB,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAC7C,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,iCAAiC,EAAE,KAAK,EAAC,KAAK,EAAC,EAAE;YAC1D,MAAM,IAAI,CAAC,iBAAiB,EAAE,YAAY,EAAE,CAAC;YAC7C,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC1D,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,mCAAmC,EAAE,KAAK,EAAC,KAAK,EAAC,EAAE;YAC5D,MAAM,IAAI,CAAC,iBAAiB,EAAE,YAAY,EAAE,CAAC;YAC7C,IAAI,CAAC,4BAA4B,CAAC,KAAK,CAAC,kBAAkB,EAAE,OAAO,CAAC,CAAC;QACvE,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,IAAI,CAAC,iBAAiB,EAAE,YAAY,EAAE,CAAC;YAC7C,IAAI,CAAC,2BAA2B,CAAC,OAAO,CAAC,CAAC;QAC5C,CAAC,CAAC,CAAC;QACH,OAAO,CAAC,EAAE,CAAC,qBAAqB,EAAE,KAAK,EAAC,KAAK,EAAC,EAAE;YAC9C,MAAM,IAAI,CAAC,iBAAiB,EAAE,YAAY,EAAE,CAAC;YAC7C,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;QAChC,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,MAAkB;QACjC,IAAI,CAAC;YACH,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC;YAClC,IAAI,CAAC,iBAAiB,GAAG,sBAAQ,CAAC,MAAM,EAAE,CAAC;YAC3C,qEAAqE;YACrE,oEAAoE;YACpE,wEAAwE;YACxE,4DAA4D;YAC5D,MAAM,OAAO,CAAC,GAAG,CAAC;gBAChB,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC;gBACtC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC;gBAC1B,MAAM,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC,IAAI,CAAC,CAAC,EAAC,SAAS,EAAC,EAAE,EAAE;oBACpD,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;oBACzC,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC;gBACpC,CAAC,CAAC;gBACF,MAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,EAAC,OAAO,EAAE,IAAI,EAAC,CAAC;gBAC9D,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE;oBACtC,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,EAAE,4BAAkB,CAAC,CAAC;gBAC/D,CAAC,CAAC;aACH,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC;YAClC,wEAAwE;YACxE,IAAI,IAAA,0BAAW,EAAC,KAAK,CAAC,IAAI,IAAA,mCAAmB,EAAC,KAAK,CAAC,EAAE,CAAC;gBACrD,OAAO;YACT,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED,oBAAoB,CAClB,SAAiB,EACjB,UAAsB,IAAI,CAAC,OAAO;QAElC,MAAM,OAAO,GAAG,IAAI,CAAC,uBAAuB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;QACjE,IAAA,kBAAM,EAAC,OAAO,EAAE,4CAA4C,GAAG,SAAS,CAAC,CAAC;QAC1E,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,uBAAuB,CACrB,SAAiB,EACjB,UAAsB,IAAI,CAAC,OAAO;QAElC,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,EAAE,EAAE,IAAI,SAAS,EAAE,CAAC,CAAC;IACtE,CAAC;IAED,IAAI;QACF,OAAO,IAAI,CAAC,KAAK,CAAC;IACpB,CAAC;IAED,SAAS;QACP,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAAC;QACjD,IAAA,kBAAM,EAAC,SAAS,EAAE,kCAAkC,CAAC,CAAC;QACtD,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,MAAM;QACJ,OAAO,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,CAAC;IAC9C,CAAC;IAED,KAAK,CAAC,OAAe;QACnB,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI,CAAC;IAClD,CAAC;IAED,kBAAkB,CAAC,MAAiB;QAClC,IAAI,MAAM,CAAC,cAAc,EAAE,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC9C,OAAO;QACT,CAAC;QAED,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,QAAQ,CAAC,CAAC;QAC3D,IAAI,KAAK,EAAE,CAAC;YACV,KAAK,CAAC,YAAY,CAAC,MAAM,CAAC,QAAQ,EAAG,CAAC,CAAC;QACzC,CAAC;QACD,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,QAAQ,EAAG,CAAC,CAAC;QAC7C,KAAK,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,QAAQ,EAAG,CAAC,CAAC;IAC3C,CAAC;IAED,2BAA2B,CAAC,MAAkB;QAC5C,IAAI,OAAO,GAAG,IAAI,CAAC,8BAA8B,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC9D,IAAI,OAAO,KAAK,SAAS,EAAE,CAAC;YAC1B,OAAO,GAAG,IAAI,mDAA0B,CAAC,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;YACxE,IAAI,CAAC,8BAA8B,CAAC,GAAG,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;QAC3D,CAAC;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,iBAAiB,CAAC,KAAwC;QACxD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QACD,KAAK,CAAC,iBAAiB,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAC,IAAI,CAAC,CAAC;QACpD,IAAI,CAAC,IAAI,CAAC,yCAAiB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QACnD,KAAK,CAAC,IAAI,CAAC,qBAAU,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;IACnD,CAAC;IAED,sBAAsB,CAAC,OAAe;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QACD,KAAK,CAAC,iBAAiB,EAAE,CAAC;IAC5B,CAAC;IAED,sBAAsB,CAAC,OAAe;QACpC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QACD,KAAK,CAAC,iBAAiB,EAAE,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAC,yCAAiB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QACnD,KAAK,CAAC,IAAI,CAAC,qBAAU,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;IACnD,CAAC;IAED,gBAAgB,CACd,OAAmB,EACnB,SAAkC;QAElC,IAAI,SAAS,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;YAC7B,IAAI,CAAC,gBAAgB,CACnB,OAAO,EACP,SAAS,CAAC,KAAK,CAAC,EAAE,EAClB,SAAS,CAAC,KAAK,CAAC,QAAQ,CACzB,CAAC;QACJ,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC;YAC1D,KAAK,IAAI,CAAC,iBAAiB,CAAC,SAAS,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;QAC7D,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,uBAAuB,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC1D,CAAC;QAED,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;YAC3B,OAAO;QACT,CAAC;QAED,KAAK,MAAM,KAAK,IAAI,SAAS,CAAC,WAAW,EAAE,CAAC;YAC1C,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QACxC,CAAC;IACH,CAAC;IAED,gBAAgB,CACd,OAAmB,EACnB,OAAe,EACf,aAAqB;QAErB,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAChC,IAAI,KAAK,EAAE,CAAC;YACV,IAAI,OAAO,IAAI,KAAK,CAAC,UAAU,EAAE,EAAE,CAAC;gBAClC,kDAAkD;gBAClD,iDAAiD;gBACjD,yBAAyB;gBACzB,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;YAC9B,CAAC;YACD,OAAO;QACT,CAAC;QAED,KAAK,GAAG,IAAI,mBAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;QAC5D,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAChC,IAAI,CAAC,IAAI,CAAC,yCAAiB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;IACpD,CAAC;IAED,KAAK,CAAC,iBAAiB,CACrB,YAAiC,EACjC,cAA4C;QAE5C,MAAM,OAAO,GAAG,YAAY,CAAC,EAAE,CAAC;QAChC,MAAM,WAAW,GAAG,CAAC,YAAY,CAAC,QAAQ,CAAC;QAE3C,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;QAE7C,iCAAiC;QACjC,IAAI,KAAK,EAAE,CAAC;YACV,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;gBACxC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;YACvC,CAAC;QACH,CAAC;QAED,+BAA+B;QAC/B,IAAI,WAAW,EAAE,CAAC;YAChB,IAAI,KAAK,EAAE,CAAC;gBACV,wEAAwE;gBACxE,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;gBACnC,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC;YACtB,CAAC;iBAAM,CAAC;gBACN,iCAAiC;gBACjC,KAAK,GAAG,IAAI,mBAAQ,CAAC,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;YAC/D,CAAC;YACD,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;QAClC,CAAC;QAED,KAAK,GAAG,MAAM,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QACpD,KAAK,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QAC/B,IAAI,CAAC,IAAI,CAAC,yCAAiB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QACnD,KAAK,CAAC,IAAI,CAAC,qBAAU,CAAC,cAAc,EAAE,cAAc,CAAC,CAAC;IACxD,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,OAAmB,EAAE,IAAY;QAC1D,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC;QAEtC,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;YAClC,OAAO;QACT,CAAC;QAED,MAAM,OAAO,CAAC,IAAI,CAAC,uCAAuC,EAAE;YAC1D,MAAM,EAAE,iBAAiB,sBAAY,CAAC,YAAY,EAAE;YACpD,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC;QAEH,MAAM,OAAO,CAAC,GAAG,CACf,IAAI,CAAC,MAAM,EAAE;aACV,MAAM,CAAC,KAAK,CAAC,EAAE;YACd,OAAO,KAAK,CAAC,MAAM,KAAK,OAAO,CAAC;QAClC,CAAC,CAAC;aACD,GAAG,CAAC,KAAK,CAAC,EAAE;YACX,mEAAmE;YACnE,kBAAkB;YAClB,OAAO,OAAO;iBACX,IAAI,CAAC,0BAA0B,EAAE;gBAChC,OAAO,EAAE,KAAK,CAAC,GAAG;gBAClB,SAAS,EAAE,IAAI;gBACf,mBAAmB,EAAE,IAAI;aAC1B,CAAC;iBACD,KAAK,CAAC,oBAAU,CAAC,CAAC;QACvB,CAAC,CAAC,CACL,CAAC;QAEF,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IAChC,CAAC;IAED,+BAA+B,CAAC,OAAe,EAAE,GAAW;QAC1D,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QACD,KAAK,CAAC,wBAAwB,CAAC,GAAG,CAAC,CAAC;QACpC,IAAI,CAAC,IAAI,CAAC,yCAAiB,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACjE,KAAK,CAAC,IAAI,CAAC,qBAAU,CAAC,4BAA4B,EAAE,SAAS,CAAC,CAAC;QAC/D,IAAI,CAAC,IAAI,CAAC,yCAAiB,CAAC,cAAc,EAAE,KAAK,CAAC,CAAC;QACnD,KAAK,CAAC,IAAI,CAAC,qBAAU,CAAC,cAAc,EAAE,YAAY,CAAC,CAAC;IACtD,CAAC;IAED,gBAAgB,CACd,OAAe,EACf,MAA8C;QAE9C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QAClC,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QACD,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,QAAQ;gBACX,gEAAgE;gBAChE,qCAAqC;gBACrC,kEAAkE;gBAClE,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;gBACrC,MAAM;YACR,KAAK,MAAM;gBACT,IAAI,CAAC,IAAI,CAAC,yCAAiB,CAAC,YAAY,EAAE,KAAK,CAAC,CAAC;gBACjD,KAAK,CAAC,IAAI,CAAC,qBAAU,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;gBAC/C,MAAM;QACV,CAAC;IACH,CAAC;IAED,0BAA0B,CACxB,cAA4D,EAC5D,OAAmB;QAEnB,MAAM,OAAO,GAAG,cAAc,CAAC,OAAyC,CAAC;QACzE,MAAM,OAAO,GAAG,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC;QAC3C,MAAM,KAAK,GAAG,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAC5E,IAAI,KAAgC,CAAC;QACrC,IAAI,KAAK,EAAE,CAAC;YACV,sEAAsE;YACtE,IAAI,KAAK,CAAC,MAAM,KAAK,OAAO,EAAE,CAAC;gBAC7B,OAAO;YACT,CAAC;YACD,IAAI,cAAc,CAAC,OAAO,IAAI,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,EAAE,CAAC;gBAClE,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,8BAAU,CAAC,CAAC;YACnC,CAAC;iBAAM,IACL,cAAc,CAAC,IAAI,KAAK,4BAAkB;gBAC1C,CAAC,KAAK,CAAC,MAAM,CAAC,mCAAe,CAAC,CAAC,UAAU,EAAE,EAC3C,CAAC;gBACD,0EAA0E;gBAC1E,oEAAoE;gBACpE,qBAAqB;gBACrB,KAAK,GAAG,KAAK,CAAC,MAAM,CAAC,mCAAe,CAAC,CAAC;YACxC,CAAC;QACH,CAAC;QACD,sEAAsE;QACtE,IAAI,CAAC,KAAK,EAAE,CAAC;YACX,OAAO;QACT,CAAC;QACD,MAAM,OAAO,GAAG,IAAI,sCAAgB,CAClC,KAAK,EAAE,MAAM,IAAI,IAAI,CAAC,OAAO,EAC7B,cAAc,EACd,KAAK,CACN,CAAC;QACF,IAAI,KAAK,EAAE,CAAC;YACV,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAC5B,CAAC;QACD,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,EAAE,EAAE,IAAI,cAAc,CAAC,EAAE,EAAE,CAAC;QACnD,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC;IAC7C,CAAC;IAED,4BAA4B,CAC1B,kBAA0B,EAC1B,OAAmB;QAEnB,MAAM,GAAG,GAAG,GAAG,OAAO,CAAC,EAAE,EAAE,IAAI,kBAAkB,EAAE,CAAC;QACpD,MAAM,OAAO,GAAG,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAClD,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO;QACT,CAAC;QACD,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACrC,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;YACnB,OAAO,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;QAChC,CAAC;IACH,CAAC;IAED,2BAA2B,CAAC,OAAmB;QAC7C,KAAK,MAAM,CAAC,GAAG,EAAE,OAAO,CAAC,IAAI,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,EAAE,CAAC;YAChE,yDAAyD;YACzD,0BAA0B;YAC1B,IAAI,OAAO,CAAC,OAAO,KAAK,OAAO,EAAE,CAAC;gBAChC,SAAS;YACX,CAAC;YACD,IAAI,OAAO,CAAC,MAAM,EAAE,CAAC;gBACnB,OAAO,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;YAChC,CAAC;YACD,IAAI,CAAC,mBAAmB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QACvC,CAAC;IACH,CAAC;IAED,wBAAwB,CAAC,KAAe;QACtC,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,WAAW,EAAE,EAAE,CAAC;YACxC,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,CAAC;QACvC,CAAC;QACD,KAAK,CAAC,6BAAa,CAAC,EAAE,CAAC;QACvB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC,yCAAiB,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;QAClD,KAAK,CAAC,IAAI,CAAC,qBAAU,CAAC,aAAa,EAAE,KAAK,CAAC,CAAC;IAC9C,CAAC;CACF;AA/fD,oCA+fC"}