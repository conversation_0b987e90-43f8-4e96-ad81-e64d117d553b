{"version": 3, "file": "Frame.d.ts", "sourceRoot": "", "sources": ["../../../../src/api/Frame.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,KAAK,QAAQ,MAAM,mBAAmB,CAAC;AAE9C,OAAO,KAAK,EAAC,YAAY,EAAE,aAAa,EAAC,MAAM,yBAAyB,CAAC;AACzE,OAAO,KAAK,EAAC,YAAY,EAAC,MAAM,wBAAwB,CAAC;AACzD,OAAO,KAAK,EACV,IAAI,EACJ,sBAAsB,EACtB,kBAAkB,EACnB,MAAM,gBAAgB,CAAC;AACxB,OAAO,KAAK,EAAC,mBAAmB,EAAC,MAAM,+BAA+B,CAAC;AACvE,OAAO,KAAK,EAAC,kBAAkB,EAAC,MAAM,yBAAyB,CAAC;AAChE,OAAO,KAAK,EAAC,uBAAuB,EAAC,MAAM,4BAA4B,CAAC;AACxE,OAAO,EAAC,YAAY,EAAE,KAAK,SAAS,EAAC,MAAM,2BAA2B,CAAC;AAIvE,OAAO,KAAK,EACV,SAAS,EACT,YAAY,EACZ,gBAAgB,EAChB,SAAS,EACT,OAAO,EACR,MAAM,oBAAoB,CAAC;AAQ5B,OAAO,KAAK,EAAC,UAAU,EAAC,MAAM,iBAAiB,CAAC;AAChD,OAAO,KAAK,EAAC,mBAAmB,EAAC,MAAM,YAAY,CAAC;AACpD,OAAO,EAEL,KAAK,OAAO,EAEb,MAAM,wBAAwB,CAAC;AAChC,OAAO,KAAK,EAAC,KAAK,EAAC,MAAM,YAAY,CAAC;AAEtC;;GAEG;AACH,MAAM,WAAW,cAAc;IAC7B;;;;;;;;OAQG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;;;;OAKG;IACH,SAAS,CAAC,EAAE,uBAAuB,GAAG,uBAAuB,EAAE,CAAC;CACjE;AAED;;GAEG;AACH,MAAM,WAAW,WAAY,SAAQ,cAAc;IACjD;;;OAGG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;;OAGG;IACH,cAAc,CAAC,EAAE,MAAM,CAAC;CACzB;AAED;;GAEG;AACH,MAAM,WAAW,2BAA2B;IAC1C;;;;;;;;;;;OAWG;IACH,OAAO,CAAC,EAAE,KAAK,GAAG,UAAU,GAAG,MAAM,CAAC;IACtC;;;;OAIG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,MAAM,CAAC,EAAE,WAAW,CAAC;CACtB;AAED;;GAEG;AACH,MAAM,WAAW,wBAAwB;IACvC;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IACb;;;;;;OAMG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;CACb;AAED;;GAEG;AACH,MAAM,WAAW,uBAAuB;IACtC;;OAEG;IACH,GAAG,CAAC,EAAE,MAAM,CAAC;IACb;;;;;OAKG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;IACd;;OAEG;IACH,OAAO,CAAC,EAAE,MAAM,CAAC;CAClB;AAED;;GAEG;AACH,MAAM,WAAW,WAAY,SAAQ,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC;IAC7D,gBAAgB;IAChB,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC;IAC1D,gBAAgB;IAChB,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,SAAS,CAAC;IACrC,gBAAgB;IAChB,CAAC,UAAU,CAAC,cAAc,CAAC,EAAE,SAAS,CAAC;IACvC,gBAAgB;IAChB,CAAC,UAAU,CAAC,4BAA4B,CAAC,EAAE,SAAS,CAAC;IACrD,gBAAgB;IAChB,CAAC,UAAU,CAAC,aAAa,CAAC,EAAE,KAAK,CAAC;IAClC,gBAAgB;IAChB,CAAC,UAAU,CAAC,wBAAwB,CAAC,EAAE,SAAS,CAAC;CAClD;AAED;;;;;GAKG;AAEH,yBAAiB,UAAU,CAAC;IACnB,MAAM,cAAc,eAAiC,CAAC;IACtD,MAAM,YAAY,eAA+B,CAAC;IAClD,MAAM,cAAc,eAAiC,CAAC;IACtD,MAAM,4BAA4B,eAExC,CAAC;IACK,MAAM,aAAa,eAAgC,CAAC;IACpD,MAAM,wBAAwB,eAEpC,CAAC;CACH;AAED;;GAEG;AACH,eAAO,MAAM,eAAe,oGAE1B,CAAC;AAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAoDG;AACH,8BAAsB,KAAM,SAAQ,YAAY,CAAC,WAAW,CAAC;;IAC3D;;OAEG;IACH,GAAG,EAAG,MAAM,CAAC;IACb;;OAEG;IACH,SAAS,CAAC,EAAE,MAAM,CAAC;IAEnB;;OAEG;IACH,MAAM,EAAG,kBAAkB,CAAC;IAE5B;;OAEG;IACH,KAAK,CAAC,EAAE,MAAM,CAAC;IAEf;;OAEG;IACH,kBAAkB,UAAS;IAE3B;;OAEG;;IAKH;;OAEG;IACH,QAAQ,CAAC,IAAI,IAAI,IAAI;IAErB;;;OAGG;IACH,QAAQ,CAAC,UAAU,IAAI,OAAO;IAE9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAiCG;IACH,QAAQ,CAAC,IAAI,CACX,GAAG,EAAE,MAAM,EACX,OAAO,CAAC,EAAE;QACR,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,cAAc,CAAC,EAAE,MAAM,CAAC;QACxB,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,SAAS,CAAC,EAAE,uBAAuB,GAAG,uBAAuB,EAAE,CAAC;KACjE,GACA,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;IAE/B;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,QAAQ,CAAC,iBAAiB,CACxB,OAAO,CAAC,EAAE,cAAc,GACvB,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC;IAE/B;;OAEG;IACH,QAAQ,KAAK,MAAM,IAAI,UAAU,CAAC;IAElC;;OAEG;IACH,QAAQ,CAAC,SAAS,IAAI,KAAK;IAE3B;;OAEG;IACH,QAAQ,CAAC,aAAa,IAAI,KAAK;IAoB/B;;;;OAIG;IACH,mBAAmB,IAAI,IAAI;IAI3B;;OAEG;IAEG,YAAY,IAAI,OAAO,CAAC,SAAS,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC;IAiBlE;;;;;OAKG;IAEG,cAAc,CAClB,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,YAAY,CAAC,MAAM,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,EAExD,YAAY,EAAE,IAAI,GAAG,MAAM,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAQhD;;;;;OAKG;IAEG,QAAQ,CACZ,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,YAAY,CAAC,MAAM,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,EAExD,YAAY,EAAE,IAAI,GAAG,MAAM,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAQrC;;;;;;;OAOG;IACH,OAAO,CAAC,QAAQ,SAAS,MAAM,EAC7B,QAAQ,EAAE,QAAQ,GACjB,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IAE7B;;;;;;;OAOG;IACH,OAAO,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,SAAS,CAAC,GAAG,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC;IAetD;;;;;;OAMG;IAEG,CAAC,CAAC,QAAQ,SAAS,MAAM,EAC7B,QAAQ,EAAE,QAAQ,GACjB,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC;IAMnD;;;;;;OAMG;IAEG,EAAE,CAAC,QAAQ,SAAS,MAAM,EAC9B,QAAQ,EAAE,QAAQ,GACjB,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;IAMnD;;;;;;;;;;;;;;;;;;;OAmBG;IAEG,KAAK,CACT,QAAQ,SAAS,MAAM,EACvB,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,GAAG,gBAAgB,CACzE,OAAO,CAAC,QAAQ,CAAC,EACjB,MAAM,CACP,EAED,QAAQ,EAAE,QAAQ,EAClB,YAAY,EAAE,MAAM,GAAG,IAAI,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAOrC;;;;;;;;;;;;;;;;;;;OAmBG;IAEG,MAAM,CACV,QAAQ,SAAS,MAAM,EACvB,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,gBAAgB,CAC3B,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EACxB,MAAM,CACP,GAAG,gBAAgB,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,EAAE,MAAM,CAAC,EAEtD,QAAQ,EAAE,QAAQ,EAClB,YAAY,EAAE,MAAM,GAAG,IAAI,EAC3B,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC;IAOrC;;;;;;;;;OASG;IAEG,EAAE,CAAC,UAAU,EAAE,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC;IAMjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAkCG;IAEG,eAAe,CAAC,QAAQ,SAAS,MAAM,EAC3C,QAAQ,EAAE,QAAQ,EAClB,OAAO,GAAE,sBAA2B,GACnC,OAAO,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC;IAUnD;;;;;;;;;;;;;;;;;;;;;OAqBG;IAEG,YAAY,CAChB,KAAK,EAAE,MAAM,EACb,OAAO,GAAE,sBAA2B,GACnC,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;IAOtC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgCG;IAEG,eAAe,CACnB,MAAM,SAAS,OAAO,EAAE,EACxB,IAAI,SAAS,YAAY,CAAC,MAAM,CAAC,GAAG,YAAY,CAAC,MAAM,CAAC,EAExD,YAAY,EAAE,IAAI,GAAG,MAAM,EAC3B,OAAO,GAAE,2BAAgC,EACzC,GAAG,IAAI,EAAE,MAAM,GACd,OAAO,CAAC,SAAS,CAAC,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;IAOhD;;OAEG;IAEG,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;IAkBhC;;;;;;OAMG;IACH,QAAQ,CAAC,UAAU,CACjB,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE;QACR,OAAO,CAAC,EAAE,MAAM,CAAC;QACjB,SAAS,CAAC,EAAE,uBAAuB,GAAG,uBAAuB,EAAE,CAAC;KACjE,GACA,OAAO,CAAC,IAAI,CAAC;IAEhB;;OAEG;IACG,eAAe,CAAC,OAAO,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAQrD;;;;;;;;;OASG;IACH,IAAI,IAAI,MAAM;IAId;;OAEG;IACH,QAAQ,CAAC,GAAG,IAAI,MAAM;IAEtB;;OAEG;IACH,QAAQ,CAAC,WAAW,IAAI,KAAK,GAAG,IAAI;IAEpC;;OAEG;IACH,QAAQ,CAAC,WAAW,IAAI,KAAK,EAAE;IAE/B;;OAEG;IACH,QAAQ,KAAK,QAAQ,IAAI,OAAO,CAAC;IAEjC;;;;OAIG;IACH,UAAU,IAAI,OAAO;IAIrB;;OAEG;IACH,IAAI,QAAQ,IAAI,OAAO,CAEtB;IAED;;;;;;OAMG;IAEG,YAAY,CAChB,OAAO,EAAE,wBAAwB,GAChC,OAAO,CAAC,aAAa,CAAC,iBAAiB,CAAC,CAAC;IA4D5C;;;;;OAKG;IACG,WAAW,CACf,OAAO,EAAE,IAAI,CAAC,uBAAuB,EAAE,KAAK,CAAC,GAC5C,OAAO,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;IAE3C;;;;;OAKG;IACG,WAAW,CACf,OAAO,EAAE,uBAAuB,GAC/B,OAAO,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;IAqE1C;;;;;;;;;;;;;;;;;OAiBG;IAEG,KAAK,CACT,QAAQ,EAAE,MAAM,EAChB,OAAO,GAAE,QAAQ,CAAC,YAAY,CAAM,GACnC,OAAO,CAAC,IAAI,CAAC;IAOhB;;;;;OAKG;IAEG,KAAK,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAM5C;;;;;;OAMG;IAEG,KAAK,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAM5C;;;;;;;;;;;;;;;;;OAiBG;IAEG,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,GAAG,MAAM,EAAE,MAAM,EAAE,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;IAMtE;;;;;OAKG;IAEG,GAAG,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAM1C;;;;;;;;;;;;;;;;;;;;OAoBG;IAEG,IAAI,CACR,QAAQ,EAAE,MAAM,EAChB,IAAI,EAAE,MAAM,EACZ,OAAO,CAAC,EAAE,QAAQ,CAAC,mBAAmB,CAAC,GACtC,OAAO,CAAC,IAAI,CAAC;IAMhB;;;;;;;;;;;;;;;;;;;OAmBG;IACG,cAAc,CAAC,YAAY,EAAE,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC;IAMzD;;OAEG;IAEG,KAAK,IAAI,OAAO,CAAC,MAAM,CAAC;IAM9B;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,QAAQ,CAAC,mBAAmB,CAC1B,OAAO,CAAC,EAAE,kBAAkB,GAC3B,OAAO,CAAC,mBAAmB,CAAC;CAChC"}